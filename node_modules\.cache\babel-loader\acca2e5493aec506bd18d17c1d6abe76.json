{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\StrategyRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\StrategyRecord\\index.vue", "mtime": 1749804173226}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}