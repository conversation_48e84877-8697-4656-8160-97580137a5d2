<template>
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="900px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="120px"
        v-loading="loading"
      >
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入策略名称" />
        </el-form-item>

        <el-form-item label="源地址" prop="srcAddress">
          <el-select v-model="formData.srcAddress" placeholder="请选择源地址" multiple>
            <el-option
              v-for="item in addressOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="目标地址" prop="destAddress">
          <el-select v-model="formData.destAddress" placeholder="请选择目标地址" multiple>
            <el-option
              v-for="item in addressOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="服务" prop="service">
          <el-select v-model="formData.service" placeholder="请选择服务" multiple>
            <el-option
              v-for="item in serviceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="动作" prop="action">
          <el-radio-group v-model="formData.action">
            <el-radio :label="1">允许</el-radio>
            <el-radio :label="0">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="formData.priority"
            :min="1"
            :max="1000"
            placeholder="请输入优先级"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
            :rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addStrategy, updateStrategy, getStrategyInfo } from '@/api/firewall/strategyManagement'
import { getAddressSetList } from '@/api/firewall/addressSet'
import { getServiceSetList } from '@/api/firewall/serviceSet'

export default {
  name: 'AddStrategyModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      title: '新增策略',
      formData: {
        id: null,
        name: '',
        srcAddress: [],
        destAddress: [],
        service: [],
        action: 1,
        status: 1,
        priority: 100,
        remark: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入策略名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5\w]{1,30}$/, message: '字符串长度范围: 1 - 30', trigger: 'blur' },
        ],
        srcAddress: [
          { required: true, message: '请选择源地址', trigger: 'change' },
        ],
        destAddress: [
          { required: true, message: '请选择目标地址', trigger: 'change' },
        ],
        service: [
          { required: true, message: '请选择服务', trigger: 'change' },
        ],
        action: [
          { required: true, message: '请选择动作', trigger: 'change' },
        ],
        priority: [
          { required: true, message: '请输入优先级', trigger: 'blur' },
        ],
      },
      addressOptions: [],
      serviceOptions: [],
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadOptions()
      }
    },
  },
  methods: {
    async initForm() {
      if (this.currentData && this.currentData.id) {
        this.title = '编辑策略'
        this.loading = true
        try {
          const res = await getStrategyInfo({ id: this.currentData.id })
          if (res.retcode === 0) {
            this.formData = {
              id: res.data.id,
              name: res.data.name,
              srcAddress: res.data.srcAddress ? res.data.srcAddress.split(',') : [],
              destAddress: res.data.destAddress ? res.data.destAddress.split(',') : [],
              service: res.data.service ? res.data.service.split(',') : [],
              action: res.data.action,
              status: res.data.status,
              priority: res.data.priority,
              remark: res.data.remark,
            }
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('获取策略信息失败')
        } finally {
          this.loading = false
        }
      } else {
        this.title = '新增策略'
        this.formData = {
          id: null,
          name: '',
          srcAddress: [],
          destAddress: [],
          service: [],
          action: 1,
          status: 1,
          priority: 100,
          remark: '',
        }
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadOptions() {
      try {
        // 加载地址集选项
        const addressRes = await getAddressSetList({ pageSize: 1000 })
        if (addressRes.retcode === 0) {
          this.addressOptions = addressRes.data.rows || []
        }

        // 加载服务集选项
        const serviceRes = await getServiceSetList({ pageSize: 1000 })
        if (serviceRes.retcode === 0) {
          this.serviceOptions = serviceRes.data.rows || []
        }
      } catch (error) {
        console.error('加载选项失败:', error)
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const submitData = {
              ...this.formData,
              srcAddress: this.formData.srcAddress.join(','),
              destAddress: this.formData.destAddress.join(','),
              service: this.formData.service.join(','),
            }
            
            let res
            if (this.title.includes('新增')) {
              res = await addStrategy(submitData)
            } else {
              res = await updateStrategy(submitData)
            }
            
            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    handleClose() {
      this.drawerVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin: 0 10px;
}
</style>
