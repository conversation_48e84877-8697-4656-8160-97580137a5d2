import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  message,
  Form,
  Row,
  Col,
  Spin,
  Button,
  Select,
  Input,
  Radio,
} from "antd";
import SbrDrawer from "@/components/SbrDrawer";
import {fireRuleInfo,  fireRuleAdd, fireRuleUpdate } from "../services";
import core from '../../../../utils/validationUnit.js'
const { TextArea } = Input;
let Add = (props) => {
  const { refInstance, getSourceData } = props;
  const { getFieldDecorator, validateFields, setFieldsValue, getFieldValue  } = props.form;
  // 是否展示抽屉
  const [visible, setVisible] = useState(false);
  //标题
  const [title, setTitle] = useState('');
  // 查看数据
  const [record, setRecord] = useState({});
  const [loading, setLoading] = useState(false);
  useImperativeHandle(refInstance, () => ({
    showDrawer,
  }));

  useEffect(() => {}, []);

  // 打开抽屉
  const showDrawer = async (record = {}) => {
    setVisible(true);

    if (record.id) {
      setTitle('编辑服务集');
      try {
        setLoading(true);
        const res = await fireRuleInfo({ id: record.id });
        if (res.retcode == 0) {
          setFieldsValue({
            id: res.data.id,
            name: res.data.name,
            protocol: res.data.protocol,
            port: res.data.port,
            remark: res.data.remark,
            endPort: res.data.endPort,
            slEndPort: res.data.slEndPort,
            slPort: res.data.slPort,            
          });
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
      }
    }else{
      setTitle('新增服务集');
    }
  };
  const  validatorName  =(rule, value, callback) =>{
    let reg = '/^[\\u4E00-\\u9FA5a-zA-Z0-9\\-_]{1,15}$/'
    if (core.regex(value, [reg]) && value.replace(/[\u4E00-\u9FA5]/g, '111').length <= 15) {
      callback()
    } else {
      callback(new Error('1-15个字母、数字、减号、中文、下划线的组合。'))
    }
    }
  //关闭抽屉
  const onDrawerClose = () => {
    setVisible(false);
  };
  // 提交
  const handleSubmit = (e) => {
    e.preventDefault();
    validateFields(async (err, values) => {
      if (!err) {
        let res 
        if(title.indexOf('新增')!=-1){
           res = await fireRuleAdd(values);
        }else{
          res = await fireRuleUpdate(values);
        }      
        if (res.retcode == 0) {
          message.success('操作成功');
          getSourceData();
          onDrawerClose();
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      }
    });
  };

  const validateSourceNumber =(rule, value, callback)=>{
    let startNum = Number.parseInt(getFieldValue('endPort'));
    let endNum = Number.parseInt(getFieldValue('port'));
    if(!Number.isNaN(startNum) && !Number.isNaN(endNum) && startNum < endNum){
      callback('低端口不能大于高端口');
    }
    callback()
  }
  const validatePurposeNumber =(rule, value, callback)=>{
    let startNum = Number.parseInt(getFieldValue('slEndPort'));
    let endNum = Number.parseInt(getFieldValue('slPort'));
    if(!Number.isNaN(startNum) && !Number.isNaN(endNum) && startNum < endNum){
      callback('低端口不能大于高端口');
    }
    callback()
  }
  
  return (
    <SbrDrawer
      title={title}
      width={800}
      onClose={onDrawerClose}
      visible={visible}
    >
      <Form
        onSubmit={handleSubmit}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <div className="public-block">
          <Spin spinning={loading}>
            <Form.Item hidden>
              {getFieldDecorator("id", {})(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
            <Form.Item label="名称">
              {getFieldDecorator("name", {
                rules: [
                  {
                    required: true,
                    message: "请输入名称",
                  },
                  { validator: validatorName },  
                ],
              })(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
            <Form.Item label="协议">
              {getFieldDecorator("protocol", {
                  rules: [
                    {
                      required: true,
                      message: "请选择状态",
                    },
                  ],
              })(
                <Select placeholder="请选择协议">
                  <Option value="TCP">TCP</Option>
                  <Option value="UDP">UDP</Option>                
                </Select>
              )}
            </Form.Item>
            <Form.Item label="源端口高端口">
              {getFieldDecorator("endPort", {
                rules: [
                  {
                    required: true,
                    message: "请输入源端口高端口",
                  },
                  {
                    pattern: /^((6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])|[0-5]?\d{0,4})$/,
                    message: '端口范围0-65535',
                  },  
                  { validator: validateSourceNumber },
                ],
              })(
                <Input placeholder="请输入源端口高端口" />
              )}
            </Form.Item>
            <Form.Item label="源端口低端口">
              {getFieldDecorator("port", {
                rules: [
                  {
                    required: true,
                    message: "请输入源端口低端口",
                  },
                  {
                    pattern: /^((6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])|[0-5]?\d{0,4})$/,
                    message: '端口范围0-65535',
                  }, 
                  { validator: validateSourceNumber },
                ],
              })(
                <Input placeholder="请输入源端口低端口" />
              )}
            </Form.Item>
            <Form.Item label="目的端口高端口">
              {getFieldDecorator("slEndPort", {
                rules: [
                  {
                    required: true,
                    message: "请输入源端口高端口",
                  },
                  {
                    pattern: /^((6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])|[0-5]?\d{0,4})$/,
                    message: '端口范围0-65535',
                  },  
                  { validator: validatePurposeNumber },
                ],
              })(
                <Input placeholder="请输入源端口高端口" />
              )}
            </Form.Item>
            <Form.Item label="目的端口低端口">
              {getFieldDecorator("slPort", {
                rules: [
                  {
                    required: true,
                    message: "请输入目的端口低端口",
                  },
                  {
                    pattern: /^((6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])|[0-5]?\d{0,4})$/,
                    message: '端口范围0-65535',
                  },  
                  { validator: validatePurposeNumber },
                ],
              })(
                <Input placeholder="请输入目的端口低端口" />
              )}
            </Form.Item>           
            <Form.Item label="备注">
              {getFieldDecorator("remark", {
                rules: [{ max: 256, message: "备注长度不超过256字" }],
              })(
                <TextArea
                  placeholder="请输入备注"
                  autoSize={{ minRows: 2, maxRows: 6 }}
                />
              )}
            </Form.Item>
          </Spin>
        </div>

        <Row type="flex" justify="center" className="drawer_btns">
          <Button htmlType="submit" type="primary">
            保存
          </Button>
          <Button className="spacing_btn" onClick={onDrawerClose}>
            关闭
          </Button>
        </Row>
      </Form>
    </SbrDrawer>
  );
};
Add = Form.create()(Add);
export default forwardRef((props, ref) => <Add {...props} refInstance={ref} />);
