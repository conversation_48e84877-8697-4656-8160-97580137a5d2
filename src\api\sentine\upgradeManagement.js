import request from '@util/requestForPy'

/**
 * 获取软件升级列表
 * @param params
 * @returns {Promise}
 */
export function getSoftwareUpgradeList(params) {
  return request({
    url: '/home_dev/sentinel_upgrade/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 上传软件升级包
 * @param data
 * @returns {Promise}
 */
export function uploadSoftwarePackage(data) {
  return request({
    url: '/home_dev/sentinel_upgrade/upload',
    method: 'post',
    data: data || {},
  })
}

export function getEquipmentTree(data) {
  return request({
    url: '/home_dev/audit_device/all',
    method: 'post',
    data: data || {},
  })
}

/**
 * 执行软件升级
 * @param {FormData} formData - 升级参数（包含文件）
 * @returns {Promise}
 */
export function performSoftwareUpgrade(formData) {
  return request({
    url: '/home_dev/sentinel_upgrade/upgrade',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 获取升级日志
 * @param params
 * @returns {Promise}
 */
export function getUpgradeLog(params) {
  return request({
    url: '/home_dev/sentinel_upgrade/log',
    method: 'post',
    data: params || {},
  })
}

/**
 * 获取病毒库列表
 * @param params
 * @returns {Promise}
 */
export function getVirusLibraryList(params) {
  return request({
    url: '/home_dev/sentinel_upgrade/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 上传病毒库
 * @param {FormData} formData - 病毒库文件数据
 * @returns {Promise}
 */
export function uploadVirusLibrary(formData) {
  return request({
    url: '/home_dev/sentinel_virus/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 执行病毒库更新
 * @param {FormData} formData - 更新参数（包含文件）
 * @returns {Promise}
 */
export function performVirusLibraryUpdate(formData) {
  return request({
    url: '/home_dev/sentinel_virus/perform',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 获取入侵特征库列表
 * @param params
 * @returns {Promise}
 */
export function getIntrusionFeatureList(params) {
  return request({
    url: '/home_dev/sentinel_upgrade/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 上传入侵特征库
 * @param {FormData} formData - 入侵特征库文件数据
 * @returns {Promise}
 */
export function uploadIntrusionFeature(formData) {
  return request({
    url: '/home_dev/sentinel_intrusion/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 执行入侵特征库更新
 * @param {FormData} formData - 更新参数（包含文件）
 * @returns {Promise}
 */
export function performIntrusionFeatureUpdate(formData) {
  return request({
    url: '/home_dev/sentinel_intrusion/perform',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 获取升级记录列表
 * @param params
 * @returns {Promise}
 */
export function getUpgradeRecordList(params) {
  return request({
    url: '/home_dev/sentinel_upgrade/record/all',
    method: 'post',
    data: params || {},
  })
}

/**
 * 获取升级详情
 * @param params
 * @returns {Promise}
 */
export function getUpgradeDetail(params) {
  return request({
    url: '/home_dev/sentinel_upgrade/detail',
    method: 'post',
    data: params || {},
  })
}

/**
 * 文件上传通用接口
 * @param {FormData} formData - 文件数据
 * @param {string} type - 上传类型 (package/virus/intrusion)
 * @returns {Promise}
 */
export function uploadFile(formData, type = 'package') {
  return request({
    url: `/home_dev/sentinel_upload/${type}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 删除软件升级记录
 * @param data
 * @returns {Promise}
 */
export function deleteSoftwareUpgrade(data) {
  return request({
    url: '/home_dev/sentinel_upgrade/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除病毒库记录
 * @param data
 * @returns {Promise}
 */
export function deleteVirusLibrary(data) {
  return request({
    url: '/home_dev/sentinel_virus/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除入侵特征库记录
 * @param data
 * @returns {Promise}
 */
export function deleteIntrusionFeature(data) {
  return request({
    url: '/home_dev/sentinel_intrusion/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除升级记录
 * @param data
 * @returns {Promise}
 */
export function deleteUpgradeRecord(data) {
  return request({
    url: '/home_dev/sentinel_upgrade/record/delete',
    method: 'post',
    data: data || {},
  })
}
