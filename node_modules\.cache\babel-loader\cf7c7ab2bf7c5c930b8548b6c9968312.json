{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\index.vue", "mtime": 1750063818636}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}