import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";


//策略管理-查询接口
export async function tacticsSearch(params) {
  return request(`/dev/tactics/pages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//策略管理-新增接口
export async function tacticsAdd(params) {
  return request(`/dev/tactics/add`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略管理-编辑接口
export async function tacticsUpdate(params) {
  return request(`/dev/tactics/update`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//策略管理-删除接口
export async function tacticsDelete(params) {
  return request(`/dev/tactics/delete`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略管理-批量下发
export async function protocolIssued(params) {
  return request(`/dev/tactics/protocolIssued`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略管理-同步设备协议
export async function syncFromDevice(params) {
  return request(`/dev/tactics/syncFromDevice`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略管理-启停
export async function outset(params) {
  return request(`/dev/tactics/outset`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略管理-查看
export async function tacticsInfo(params) {
  return request(`/dev/tactics/infor`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略管理-应用范围
export async function applyConfine(params) {
  return request(`/dev/tactics/applyConfine`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}







//策略管理-设备集合
export async function addInspectionData(params) {
  return request(`/dev/device/all`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}







