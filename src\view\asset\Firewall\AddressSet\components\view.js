import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { message, Row, Button, Spin, Col } from 'antd';
import SbrDrawer from '@/components/SbrDrawer';
import moment from 'moment';
import { ipAddressInfo } from '../services';

let View = (props) => {
  const { refInstance } = props;

  // 是否展示抽屉
  const [visible, setVisible] = useState(false);
  // 查看数据
  const [record, setRecord] = useState({});
  const [loading, setLoading] = useState(false);

  useImperativeHandle(refInstance, () => ({
    showDrawer,
  }));

  useEffect(() => {}, []);

  // 打开抽屉
  const showDrawer = async (record = {}) => {
    setVisible(true);
    if (record.id) {
      try {
        setLoading(true);
        const res = await ipAddressInfo({ id: record.id });
        if (res.retcode == 0) {
          setRecord(res.data);
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
      }
    }
  };

  //关闭抽屉
  const onDrawerClose = () => {
    setVisible(false);
    setRecord({});
  };

  return (
    <SbrDrawer title="查看地址集" width={800} onClose={onDrawerClose} visible={visible}>
      <Spin spinning={loading}>
        <div className="public-block">
          <div className="public-block-details">
            <div className="public-block-row">
              <span className="public-block-row-title">地址名称：</span>
              {record.name}
            </div>
            <div className="public-block-row">
              <span className="public-block-row-title">来源设备：</span>
              {record.srcDeviceName}
            </div>
            <div className="public-block-row">
              <span className="public-block-row-title">来源ip：</span>
              {record.srcIp}
            </div>          
          </div>
        </div>
      </Spin>
      <Row type="flex" justify="center" className="drawer_btns">
        <Button className="spacing_btn" onClick={onDrawerClose}>
          关闭
        </Button>
      </Row>
    </SbrDrawer>
  );
};

export default forwardRef((props, ref) => <View {...props} refInstance={ref} />);
