{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\AddressSet\\components\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\AddressSet\\components\\DeviceComponent.vue", "mtime": 1750123352054}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0IjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3BsaXQiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3RhcnRzLXdpdGgiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovd29ya3NwYWNlL3NtcC9ub2RlX21vZHVsZXMvQHZ1ZS9iYWJlbC1wcmVzZXQtYXBwL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyIjsKaW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tICJEOi93b3Jrc3BhY2Uvc21wL25vZGVfbW9kdWxlcy9AdnVlL2JhYmVsLXByZXNldC1hcHAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5IjsKaW1wb3J0ICJyZWdlbmVyYXRvci1ydW50aW1lL3J1bnRpbWUiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovd29ya3NwYWNlL3NtcC9ub2RlX21vZHVsZXMvQHZ1ZS9iYWJlbC1wcmVzZXQtYXBwL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KaW1wb3J0IHsgZ2V0RGV2aWNlTGlzdCwgaXNzdWVBZGRyZXNzU2V0LCBzeW5jQWRkcmVzc1NldEZyb21EZXZpY2UgfSBmcm9tICdAL2FwaS9maXJld2FsbC9hZGRyZXNzU2V0JzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEZXZpY2VDb21wb25lbnQnLAogIHByb3BzOiB7CiAgICBvcGVyYXRpb25UeXBlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzEnIC8vICcxJzog5LiL5Y+RLCAnMic6IOWQjOatpQoKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkcmF3ZXJWaXNpYmxlOiBmYWxzZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHRpdGxlOiAn5LiL5Y+R5Y2P6K6uJywKICAgICAgZm9ybURhdGE6IHsKICAgICAgICBkZXZpY2VJZHM6IFtdCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZGV2aWNlSWRzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup6K6+5aSHJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgZGV2aWNlRGF0YTogW10sCiAgICAgIHNlbGVjdGVkRGV2aWNlSWRzOiBbXSwKICAgICAgY3VycmVudFJlY29yZDoge30sCiAgICAgIGN1cnJlbnRJZHM6IFtdLAogICAgICB0cmVlUHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogJ2NoaWxkTGlzdCcsCiAgICAgICAgbGFiZWw6ICduYW1lJywKICAgICAgICBkaXNhYmxlZDogZnVuY3Rpb24gZGlzYWJsZWQoZGF0YSkgewogICAgICAgICAgcmV0dXJuIGRhdGEudHlwZSA9PT0gJzAnOwogICAgICAgIH0gLy8g5YiG57uE6IqC54K556aB55SoCgogICAgICB9CiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIG9wZXJhdGlvblR5cGU6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcih2YWwpIHsKICAgICAgICB0aGlzLnRpdGxlID0gdmFsID09PSAnMScgPyAn5LiL5Y+R5Y2P6K6uJyA6ICflkIzmraXorr7lpIflnLDlnYAnOwogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIHNob3dEcmF3ZXI6IGZ1bmN0aW9uIHNob3dEcmF3ZXIoKSB7CiAgICAgIHZhciBfYXJndW1lbnRzID0gYXJndW1lbnRzLAogICAgICAgICAgX3RoaXMgPSB0aGlzOwoKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgcmVjb3JkLCBpZHM7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgcmVjb3JkID0gX2FyZ3VtZW50cy5sZW5ndGggPiAwICYmIF9hcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IF9hcmd1bWVudHNbMF0gOiB7fTsKICAgICAgICAgICAgICAgIGlkcyA9IF9hcmd1bWVudHMubGVuZ3RoID4gMSAmJiBfYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBfYXJndW1lbnRzWzFdIDogW107CiAgICAgICAgICAgICAgICBfdGhpcy5jdXJyZW50UmVjb3JkID0gcmVjb3JkOwogICAgICAgICAgICAgICAgX3RoaXMuY3VycmVudElkcyA9IGlkczsKICAgICAgICAgICAgICAgIF90aGlzLnNlbGVjdGVkRGV2aWNlSWRzID0gX3RvQ29uc3VtYWJsZUFycmF5KGlkcyk7CiAgICAgICAgICAgICAgICBfdGhpcy5kcmF3ZXJWaXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA4OwogICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzLmxvYWREZXZpY2VEYXRhKCk7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBsb2FkRGV2aWNlRGF0YTogZnVuY3Rpb24gbG9hZERldmljZURhdGEoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHJlczsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAxOwogICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSA0OwogICAgICAgICAgICAgICAgcmV0dXJuIGdldERldmljZUxpc3QoewogICAgICAgICAgICAgICAgICBpZDogX3RoaXMyLmN1cnJlbnRSZWNvcmQuaWQKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDIuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRldmljZURhdGEgPSBfdGhpczIudHJhbnNmb3JtVHJlZURhdGEocmVzLmRhdGEgfHwgW10pOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSA4OwogICAgICAgICAgICAgICAgX2NvbnRleHQyLnQwID0gX2NvbnRleHQyWyJjYXRjaCJdKDEpOwoKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6+5aSH5YiX6KGo5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTE6CiAgICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDExOwogICAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuZmluaXNoKDExKTsKCiAgICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMiwgbnVsbCwgW1sxLCA4LCAxMSwgMTRdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHRyYW5zZm9ybVRyZWVEYXRhOiBmdW5jdGlvbiB0cmFuc2Zvcm1UcmVlRGF0YShkYXRhKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgcmV0dXJuIGRhdGEubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdmFyIG5vZGUgPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGl0ZW0pLCB7fSwgewogICAgICAgICAgdmFsdWU6ICIiLmNvbmNhdChpdGVtLnR5cGUsICIsIikuY29uY2F0KGl0ZW0uY29tcElkLCAiLCIpLmNvbmNhdChpdGVtLnNyY0lkKSwKICAgICAgICAgIGRpc2FibGVkOiBpdGVtLnR5cGUgPT09ICcwJyAvLyDliIbnu4ToioLngrnnpoHnlKjpgInmi6kKCiAgICAgICAgfSk7CgogICAgICAgIGlmIChpdGVtLmNoaWxkTGlzdCAmJiBpdGVtLmNoaWxkTGlzdC5sZW5ndGggPiAwKSB7CiAgICAgICAgICBub2RlLmNoaWxkTGlzdCA9IF90aGlzMy50cmFuc2Zvcm1UcmVlRGF0YShpdGVtLmNoaWxkTGlzdCk7CiAgICAgICAgfQoKICAgICAgICByZXR1cm4gbm9kZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVHJlZUNoZWNrOiBmdW5jdGlvbiBoYW5kbGVUcmVlQ2hlY2soY2hlY2tlZE5vZGVzLCBjaGVja2VkSW5mbykgewogICAgICAvLyDmj5Dlj5borr7lpIdJRO+8iHR5cGXkuLonMSfnmoToioLngrnvvIkKICAgICAgdmFyIGRldmljZUlkcyA9IFtdOwogICAgICB2YXIgYWxsQ2hlY2tlZE5vZGVzID0gY2hlY2tlZEluZm8uY2hlY2tlZE5vZGVzIHx8IFtdOwogICAgICBhbGxDaGVja2VkTm9kZXMuZm9yRWFjaChmdW5jdGlvbiAobm9kZSkgewogICAgICAgIGlmIChub2RlLnZhbHVlICYmIG5vZGUudmFsdWUuc3RhcnRzV2l0aCgnMSwnKSkgewogICAgICAgICAgdmFyIHBhcnRzID0gbm9kZS52YWx1ZS5zcGxpdCgnLCcpOwoKICAgICAgICAgIGlmIChwYXJ0cy5sZW5ndGggPj0gMykgewogICAgICAgICAgICBkZXZpY2VJZHMucHVzaChwYXJ0c1syXSk7IC8vIHNyY0lkCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5mb3JtRGF0YS5kZXZpY2VJZHMgPSBkZXZpY2VJZHM7CiAgICB9LAogICAgaGFuZGxlU3VibWl0OiBmdW5jdGlvbiBoYW5kbGVTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwoKICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkgewogICAgICAgIHZhciBfcmVmID0gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlNCh2YWxpZCkgewogICAgICAgICAgdmFyIGNvbmZpcm1NZXNzYWdlOwogICAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0NC5wcmV2ID0gX2NvbnRleHQ0Lm5leHQpIHsKICAgICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICAgICAgY29uZmlybU1lc3NhZ2UgPSBfdGhpczQub3BlcmF0aW9uVHlwZSA9PT0gJzEnID8gJ+S4i+WPkeWNj+iuruWQjuS4jeWPr+S/ruaUue+8jOaYr+WQpuehruiupOS4i+WPke+8nycgOiAn5ZCM5q2l6K6+5aSH5Y2P6K6u5ZCO5LiN5Y+v5L+u5pS577yM5piv5ZCm56Gu6K6k5ZCM5q2l6K6+5aSH5Y2P6K6u77yfJzsKCiAgICAgICAgICAgICAgICAgICAgX3RoaXM0LiRjb25maXJtKGNvbmZpcm1NZXNzYWdlLCAn56Gu6K6k5pON5L2cJywgewogICAgICAgICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7orqQnLAogICAgICAgICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICAgICAgICB9KS50aGVuKCAvKiNfX1BVUkVfXyovX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICAgICAgICAgICAgICAgIHZhciByZXMsIHBhcmFtczsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNC5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSAxOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJhbXMgPSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFuZERldmljZUlkczogX3RoaXM0LmZvcm1EYXRhLmRldmljZUlkcy5qb2luKCcsJyksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRzOiBfdGhpczQuY3VycmVudElkcy5qb2luKCcsJykKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghKF90aGlzNC5vcGVyYXRpb25UeXBlID09PSAnMScpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA5OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDY7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpc3N1ZUFkZHJlc3NTZXQocGFyYW1zKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0My5zZW50OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDEyOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBzeW5jQWRkcmVzc1NldEZyb21EZXZpY2UocGFyYW1zKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDMuc2VudDsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2Uuc3VjY2VzcyhyZXMubXNnIHx8ICfmk43kvZzmiJDlip8nKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXM0LiRlbWl0KCdvbi1zdWJtaXQnKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXM0LmhhbmRsZUNsb3NlKCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDE4OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDE1OgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDMucHJldiA9IDE1OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDMudDAgPSBfY29udGV4dDNbImNhdGNoIl0oMSk7CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UuZXJyb3IoJ+aTjeS9nOWksei0pScpOwoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMTg6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gMTg7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuZmluaXNoKDE4KTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDIxOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5zdG9wKCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICB9LCBfY2FsbGVlMywgbnVsbCwgW1sxLCAxNSwgMTgsIDIxXV0pOwogICAgICAgICAgICAgICAgICAgIH0pKSkuY2F0Y2goZnVuY3Rpb24gKCkgey8vIOeUqOaIt+WPlua2iOaTjeS9nAogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5zdG9wKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgICAgfSkpOwoKICAgICAgICByZXR1cm4gZnVuY3Rpb24gKF94KSB7CiAgICAgICAgICByZXR1cm4gX3JlZi5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwogICAgICAgIH07CiAgICAgIH0oKSk7CiAgICB9LAogICAgaGFuZGxlQ2xvc2U6IGZ1bmN0aW9uIGhhbmRsZUNsb3NlKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKCiAgICAgIHRoaXMuZHJhd2VyVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLmZvcm1EYXRhID0gewogICAgICAgIGRldmljZUlkczogW10KICAgICAgfTsKICAgICAgdGhpcy5zZWxlY3RlZERldmljZUlkcyA9IFtdOwogICAgICB0aGlzLmN1cnJlbnRSZWNvcmQgPSB7fTsKICAgICAgdGhpcy5jdXJyZW50SWRzID0gW107CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuJHJlZnMuZm9ybSAmJiBfdGhpczUuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, null]}