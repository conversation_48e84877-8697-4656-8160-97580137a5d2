import request from '@util/requestForPy'
import { stringify } from 'qs'

/**
 * 获取哨兵设备列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getSentineDeviceList(params) {
  let newParams = {}
  if (params.queryParams) {
    const queryParams = params.queryParams
    if (queryParams.fireName) newParams.name = queryParams.fireName
    if (queryParams.originIp) newParams.ip = queryParams.originIp
    if (queryParams.onlinStatus) newParams.status = queryParams.onlinStatus
    if (queryParams.group_id) newParams.group_id = queryParams.group_id
  }
  newParams.category = params.type
  newParams.page = params._page
  newParams.per_page = params._limit

  return request(`/api2/device/list?${stringify(newParams)}`, {
    method: 'GET',
    Headers: {
      'Content-Type': 'application/json'
    },
  })
}

/**
 * 新增哨兵设备
 * @param {Object} data - 设备数据
 * @returns {Promise}
 */
export function addSentineDevice(data) {
  return request('/api2/device/add', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 编辑哨兵设备
 * @param {Object} data - 设备数据
 * @returns {Promise}
 */
export function editSentineDevice(data) {
  return request('/api2/device/edit', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 删除哨兵设备
 * @param {Object} data - 删除参数
 * @returns {Promise}
 */
export function deleteSentineDevice(data) {
  return request('/api2/device/delete', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 批量删除哨兵设备
 * @param {Object} data - 删除参数
 * @returns {Promise}
 */
export function batchDeleteSentineDevice(data) {
  return request('/api2/device/batchDelete', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 设备Ping测试
 * @param {Object} data - Ping参数
 * @returns {Promise}
 */
export function devicePing(data) {
  return request('/api2/device/ping', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 获取设备分组列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDeviceGroupList(params = {}) {
  return request('/api2/device/group/list', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...params
    }
  })
}

/**
 * 获取设备用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDeviceUserList(params) {
  return request('/api2/device/user/list', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...params
    }
  })
}

/**
 * 添加设备用户
 * @param {Object} data - 用户数据
 * @returns {Promise}
 */
export function addDeviceUser(data) {
  return request('/api2/device/user/add', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 登录日志记录
 * @param {Object} data - 日志数据
 * @returns {Promise}
 */
export function saveLoginLog(data) {
  return request('/api2/device/login/log', {
    method: 'POST',
    Headers: {
      'Content-Type': 'application/json',
    },
    body: {
      ...data
    }
  })
}

/**
 * 获取设备树数据（用于授权管理）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDeviceTreeData(params) {
  return request({
    url: '/home_dev/audit_device/all',
    method: 'post',
    data: params || {},
  })
}
