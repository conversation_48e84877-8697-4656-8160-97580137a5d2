import request from '@util/requestForPy'
import { stringify } from 'qs'

/**
 * 获取哨兵设备列表
 * @param params
 * @returns {Promise}
 */
export function getSentineDeviceList(params) {
  let newParams = {}
  if (params.queryParams) {
    const queryParams = params.queryParams
    if (queryParams.fireName) newParams.name = queryParams.fireName
    if (queryParams.originIp) newParams.ip = queryParams.originIp
    if (queryParams.onlinStatus) newParams.status = queryParams.onlinStatus
    if (queryParams.group_id) newParams.group_id = queryParams.group_id
  }
  newParams.category = params.type
  newParams.page = params._page
  newParams.per_page = params._limit

  return request({
    url: `/api2/device/list?${stringify(newParams)}`,
    method: 'get',
    // get 请求无需 data
  })
}

/**
 * 新增哨兵设备
 * @param data
 * @returns {Promise}
 */
export function addSentineDevice(data) {
  return request({
    url: '/api2/device/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 编辑哨兵设备
 * @param data
 * @returns {Promise}
 */
export function editSentineDevice(data) {
  return request({
    url: '/api2/device/edit',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除哨兵设备
 * @param data
 * @returns {Promise}
 */
export function deleteSentineDevice(data) {
  return request({
    url: '/api2/device/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 批量删除哨兵设备
 * @param data
 * @returns {Promise}
 */
export function batchDeleteSentineDevice(data) {
  return request({
    url: '/api2/device/batchDelete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 设备Ping测试
 * @param data
 * @returns {Promise}
 */
export function devicePing(data) {
  return request({
    url: '/api2/device/ping',
    method: 'post',
    data: data || {},
  })
}

/**
 * 获取设备分组列表
 * @param params
 * @returns {Promise}
 */
export function getDeviceGroupList(params = {}) {
  return request({
    url: '/api2/device/group/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 获取设备用户列表
 * @param params
 * @returns {Promise}
 */
export function getDeviceUserList(params) {
  return request({
    url: '/api2/device/user/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 添加设备用户
 * @param data
 * @returns {Promise}
 */
export function addDeviceUser(data) {
  return request({
    url: '/api2/device/user/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 登录日志记录
 * @param data
 * @returns {Promise}
 */
export function saveLoginLog(data) {
  return request({
    url: '/api2/device/login/log',
    method: 'post',
    data: data || {},
  })
}
