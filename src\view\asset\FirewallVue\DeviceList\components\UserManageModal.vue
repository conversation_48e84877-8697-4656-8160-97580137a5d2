<template>
  <el-dialog
    title="用户管理"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="user-manage-content">
      <div class="toolbar">
        <el-button type="primary" @click="handleAddUser">新增用户</el-button>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <el-table
        :data="userList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        size="mini"
        height="400"
      >
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名"></el-table-column>
        <el-table-column prop="role" label="角色"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span :class="scope.row.status === 1 ? 'status-active' : 'status-inactive'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
          <template slot-scope="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="handleEditUser(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDeleteUser(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      :title="userFormTitle"
      :visible.sync="userFormVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="userForm"
        :model="userFormData"
        :rules="userFormRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userFormData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="userFormData.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userFormData.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="操作员" value="operator"></el-option>
            <el-option label="查看者" value="viewer"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userFormData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="userFormVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUserSubmit" :loading="userSubmitLoading">确认</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { getDeviceUserList, addDeviceUser, deleteDeviceUser } from '@/api/firewall/deviceManagement'
import dayjs from 'dayjs'

export default {
  name: 'UserManageModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      userList: [],
      selectedUsers: [],
      userFormVisible: false,
      userFormTitle: '新增用户',
      userSubmitLoading: false,
      userFormData: {
        id: null,
        username: '',
        password: '',
        role: 'operator',
        status: 1,
      },
      userFormRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'change' },
        ],
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadUserList()
      }
    },
  },
  methods: {
    async loadUserList() {
      if (!this.currentData.id) return
      
      this.loading = true
      try {
        const res = await getDeviceUserList({ deviceId: this.currentData.id })
        if (res.retcode === 0) {
          this.userList = res.data || []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },
    handleAddUser() {
      this.userFormTitle = '新增用户'
      this.userFormData = {
        id: null,
        username: '',
        password: '',
        role: 'operator',
        status: 1,
      }
      this.userFormVisible = true
      this.$nextTick(() => {
        this.$refs.userForm && this.$refs.userForm.clearValidate()
      })
    },
    handleEditUser(row) {
      this.userFormTitle = '编辑用户'
      this.userFormData = {
        id: row.id,
        username: row.username,
        password: '', // 编辑时不显示原密码
        role: row.role,
        status: row.status,
      }
      this.userFormVisible = true
      this.$nextTick(() => {
        this.$refs.userForm && this.$refs.userForm.clearValidate()
      })
    },
    handleDeleteUser(row) {
      this.$confirm('确定要删除该用户吗？', '删除确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        try {
          const res = await deleteDeviceUser({ 
            deviceId: this.currentData.id,
            userId: row.id 
          })
          if (res.retcode === 0) {
            this.$message.success('删除成功')
            this.loadUserList()
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    handleBatchDelete() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请选择要删除的用户')
        return
      }
      
      this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？`, '批量删除确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        try {
          const userIds = this.selectedUsers.map(user => user.id)
          const res = await deleteDeviceUser({ 
            deviceId: this.currentData.id,
            userIds: userIds.join(',')
          })
          if (res.retcode === 0) {
            this.$message.success('删除成功')
            this.loadUserList()
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    handleUserSubmit() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          this.userSubmitLoading = true
          try {
            const params = {
              ...this.userFormData,
              deviceId: this.currentData.id,
            }
            
            const res = await addDeviceUser(params)
            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.userFormVisible = false
              this.loadUserList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.userSubmitLoading = false
          }
        }
      })
    },
    handleSelectionChange(selection) {
      this.selectedUsers = selection
    },
    formatTime(time) {
      if (!time) return '-'
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleClose() {
      this.dialogVisible = false
      this.userList = []
      this.selectedUsers = []
    },
  },
}
</script>

<style lang="scss" scoped>
.user-manage-content {
  .toolbar {
    margin-bottom: 16px;
  }
}

.status-active {
  color: #67c23a;
}

.status-inactive {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
