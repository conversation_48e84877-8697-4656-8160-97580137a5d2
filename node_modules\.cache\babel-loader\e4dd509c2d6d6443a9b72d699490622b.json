{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\BackupRestore\\components\\RestoreModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\BackupRestore\\components\\RestoreModal.vue", "mtime": 1750124417794}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}