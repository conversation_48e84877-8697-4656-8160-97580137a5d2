import request from '@util/requestForPy'

/**
 * 获取哨兵策略列表
 * @param params
 * @returns {Promise}
 */
export function getStrategyList(params) {
  return request({
    url: '/home_dev/sentinel_tactics/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 新增哨兵策略
 * @param data
 * @returns {Promise}
 */
export function addStrategy(data) {
  return request({
    url: '/home_dev/sentinel_tactics/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 编辑哨兵策略
 * @param data
 * @returns {Promise}
 */
export function updateStrategy(data) {
  return request({
    url: '/home_dev/sentinel_tactics/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除哨兵策略
 * @param data
 * @returns {Promise}
 */
export function deleteStrategy(data) {
  return request({
    url: '/home_dev/sentinel_tactics/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略下发
 * @param data
 * @returns {Promise}
 */
export function distributeStrategy(data) {
  return request({
    url: '/home_dev/sentinel_tactics/distribute',
    method: 'post',
    data: data || {},
  })
}

/**
 * 批量策略下发
 * @param data
 * @returns {Promise}
 */
export function batchDistributeStrategy(data) {
  return request({
    url: '/home_dev/sentinel_tactics/batchDistribute',
    method: 'post',
    data: data || {},
  })
}

/**
 * 获取策略下发设备列表
 * @param params
 * @returns {Promise}
 */
export function getStrategyDeviceList(params) {
  return request({
    url: '/home_dev/sentinel_tactics/device/list',
    method: 'post',
    data: params || {},
  })
}
