import request from '@/util/request'

/**
 * 升级管理-查询接口
 * @param params
 * @returns {Promise}
 */
export function getUpgradeList(params) {
  return request({
    url: '/dev/upgrade/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 升级管理-上传升级包
 * @param data
 * @returns {Promise}
 */
export function uploadUpgradePackage(data) {
  return request({
    url: '/dev/upgrade/upload',
    method: 'post',
    data: data || {},
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 升级管理-开始升级
 * @param data
 * @returns {Promise}
 */
export function startUpgrade(data) {
  return request({
    url: '/dev/upgrade/start',
    method: 'post',
    data: data || {},
  })
}

/**
 * 升级管理-查询升级进度
 * @param params
 * @returns {Promise}
 */
export function getUpgradeProgress(params) {
  return request({
    url: '/dev/upgrade/progress',
    method: 'post',
    data: params || {},
  })
}

/**
 * 升级管理-删除升级包
 * @param data
 * @returns {Promise}
 */
export function deleteUpgradePackage(data) {
  return request({
    url: '/dev/upgrade/delete',
    method: 'post',
    data: data || {},
  })
}
