<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search" style="flex:1;display: flex;align-items: center;">
          授权状态：
          <el-select v-model="queryInput.liceneStatus" placeholder="授权状态" clearable style="width: 240px;margin-right: 24px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="有效" value="0"></el-option>
            <el-option label="过期" value="1"></el-option>
          </el-select>
          许可证类型：
          <el-select v-model="queryInput.licenceCategory" placeholder="许可证类型" clearable style="width: 240px;margin-right: 24px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="IPS" value="IPS"></el-option>
            <el-option label="SOFT" value="SOFT"></el-option>
            <el-option label="AntiVirus" value="AntiVirus"></el-option>
          </el-select>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </section>
        <section class="table-header-button">
          <el-button type="primary" @click="handleAdd">新建授权</el-button>
          <el-button v-if="showSyncButton" @click="handleSync">同步信息</el-button>
        </section>
      </section>
    </header>
    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">授权管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
        >
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="授权状态">
            <template slot-scope="scope">
              {{ scope.row.status === 0 ? '有效' : '过期' }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称"></el-table-column>
          <el-table-column prop="proc" label="许可证类型"></el-table-column>
          <el-table-column label="到期时间">
            <template slot-scope="scope">
              {{ scope.row.proc === 'SOFT' ? '永久' : formatDate(scope.row.authTime) }}
            </template>
          </el-table-column>
          <el-table-column label="获取设备许可证时间">
            <template slot-scope="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button class="el-button--blue" type="text" @click="handleUpdate(scope.row)">重新授权</el-button>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>

    <!-- 新增/更新授权对话框 -->
    <add-update-modal :visible.sync="addUpdateModalVisible" @on-submit="handleAddUpdateSubmit"></add-update-modal>

    <!-- 设备组件 -->
    <device-component ref="deviceComponent"></device-component>
  </div>
</template>

<script>
import AddUpdateModal from './component/AddUpdateModal.vue'
import DeviceComponent from './component/DeviceComponent.vue'
import { getAuthorizationList } from '@/api/sentine/authManagement'
import moment from 'moment'

export default {
  name: 'AuthManagement',
  components: {
    AddUpdateModal,
    DeviceComponent,
  },
  data() {
    return {
      loading: false,
      queryInput: {
        liceneStatus: '',
        licenceCategory: '',
      },
      tableData: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addUpdateModalVisible: false,
      showSyncButton: true,
    }
  },
  mounted() {
    this.getAuthList()
    // this.checkSyncButton()
  },
  methods: {
    checkSyncButton() {
      // 检查当前路径是否显示同步按钮
      // this.showSyncButton = location.pathname === '/sentine/system/authmanagement'
    },
    async getAuthList() {
      this.loading = true
      const payload = {
        pageSize: this.pagination.pageSize,
        pageIndex: this.pagination.currentPage,
        status: this.queryInput.liceneStatus,
        proc: this.queryInput.licenceCategory,
      }

      try {
        const res = await getAuthorizationList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取授权列表失败')
      } finally {
        this.loading = false
      }
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getAuthList()
    },
    handleReset() {
      this.queryInput = {
        liceneStatus: '',
        licenceCategory: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.addUpdateModalVisible = true
    },
    handleUpdate(record) {
      this.addUpdateModalVisible = true
    },
    handleSync() {
      this.$refs.deviceComponent.showDrawer()
    },
    handleAddUpdateSubmit() {
      this.addUpdateModalVisible = false
      this.getAuthList()
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getAuthList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getAuthList()
    },
    formatDate(dateString) {
      return dateString ? moment(dateString).format('YYYY/MM/DD') : ''
    },
  },
}
</script>

<style lang="scss" scoped>
.el-button--blue {
  color: #409eff;
}

.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
