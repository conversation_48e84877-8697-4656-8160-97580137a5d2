import React, { Component, Fragment } from "react";
import {
  Card,
  Button,
  Table,
  Tabs,
  Pagination,
  Row,
  Col,
  Typography,
  Form,
  Select,
  Input,
  Modal,
  message,
  Progress
} from "antd";
import { connect } from "dva";
import moment from "moment";
import AddDeviceModal from "./components/AddDeviceModal";
import styles from "./index.less";

const { Column } = Table;
const FormItem = Form.Item;
const { Text } = Typography;
import custom from "../../config/custom.js";

const { primaryColor } = custom;

@Form.create()
@connect(({ }) => ({

}))
export default class FireWall extends Component {
  constructor(props) {
    super(props);
    this.newTabIndex = 0;
    this.state = {
      currentPage: 1,
      currentPageSize: 10,
      currentUserLimit: 10,
      userTotal: 0,
      selectedRowKeys: [],
      selectedDevList: [],
      searchValue: "",
      visible: false,
      restoreList:{},
      visiableRestore:false,
      loading:false
    };
  }


  componentWillMount() {
  
  }

  componentDidMount() {
    this.getDeviceList();
  }



  /**
   * 获取设备还原列表数据
   */
  getDeviceList = () => {
    const { dispatch } = this.props;
    this.setState({
      loading:true
    })
    dispatch({
      type: "restore/searchBackupData",
      payload: {
        // queryParams: this.state.searchValue,
        pageSize: this.state.currentPageSize,
        pageIndex: this.state.currentPage,
        deviceIp: this.state.searchValue.deviceIp,
        deviceName: this.state.searchValue.deviceName,
      },
      callback: (res) => {
        //判断是否成功
        if (res.retcode == 0) {
          this.setState({
            restoreList:res.data,
          });
        } else {
          message.error(res.message);
        }
        this.setState({
          loading:false
        })
      },
    });
  };


  // /**
  //  * 设备选择改变事件
  //  * @param selectedRowKeys
  //  */
  // onSelectChange = (selectedRowKeys, selectedRows) => {
  //   this.setState({
  //     selectedRowKeys,
  //     selectedDevList: selectedRows.map((item) => item.ip),
  //   });
  // };



  /**
   * 设备列表分页大小改变事件
   * @param current
   * @param pageSize
   */
  onShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        currentPage: current,
        currentPageSize: pageSize,
      },
      () => {
        this.getDeviceList();
      }
    );
  };


  /**
   * 页面改变处理事件
   * @param pageNumber
   */
  handlePageChange = (pageNumber) => {
    this.setState(
      {
        currentPage: pageNumber,
      },
      () => {
        this.getDeviceList();
      }
    );
  };

 
  /**
   * 显示总数逻辑
   * @param total
   * @returns {string}
   */
  showTotal = (total) => {
    const {
      restoreList
    } = this.state;
    return `总数据${restoreList.total}条`;
  };


  /**
   * 按条件查询设备
   */
  handleAdvancedSearch = () => {
    const { form } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        let searchValue = {};
        if (values.deviceName) searchValue.deviceName = values.deviceName;
        if (values.deviceIp) searchValue.deviceIp = values.deviceIp;       
        this.setState(
          {
            searchValue: searchValue,
            currentPage: 1,
          },
          () => {
            this.getDeviceList();
          }
        );
      }
    });
  };
  handleReset = () => {
    const { resetFields } = this.props.form;
    resetFields();
  };

  /**
   * 新建
   */
   handleAddClick = () => {
    this.setState({
      visiableRestore:true
    })
  }
  handleAddCancel = () => {
      this.setState({
        visiableRestore:false
      })
  }

  /**
   * 
   * @param record
   */
   handleRestore = (record) => { 
    Modal.confirm({
      title: "还原",
      content: `确定要对设备配置还原吗？`,
      okText: "确认",
      cancelText: "取消",
      centered: true,
      onOk:  () => {
        return new Promise((resolve,reject)=>{
          const { dispatch } = this.props;
          dispatch({
            type: "restore/recoverBackUpData",
            payload: {
              id: record.id,           
            },
            callback: (res) => {
              //判断是否成功
              if (res.retcode == 0) {
                this.getDeviceList();
              } else {
                message.error(res.msg);
              }
              resolve()
            },
          });
        }).catch((err) => {
          console.log(err);
        });
      },
    });
  };


  //  * 批量删除设备
  //  * @param record
  //  */
  handleDeleteClick = (record) => {
    const { dispatch } = this.props;
    let selectedRowKeys = [];
    let selectedDevList = [];
    const that = this;
    if (record.ip) {
      selectedRowKeys.push(record.id);
      selectedDevList.push(record.ip);
    } else {
      selectedRowKeys = this.state.selectedRowKeys;
      selectedDevList = this.state.selectedDevList;
    }
    if (selectedRowKeys.length) {
      Modal.confirm({
        title: "删除",
        content: `确认删除选中数据吗？`,
        // content: `删除分组将删除与分组相关的数据，该操作无法撤销。`,
        okText: "确认",
        cancelText: "取消",
        onOk: () => {
          dispatch({
            type: "fireWall/DeleteData",
            payload: {
              device_ids: selectedRowKeys,
            },
            callback: (res) => {
              if (res.code == 0) {
                // that.setState({
                //   currentPage: 1
                // });
                that.getDeviceList();
                message.success("删除成功");
                // that.delDeviceNode(selectedDevList);
              } else {
                message.error(res.msg);
              }
            },
          });
        },
      });
    } else {
      message.error("至少选中一条数据");
    }
  };

  /**
   * 处理添加设备确定事件
   * @param form
   */
  handleAddDevice = (form) => {
    const { dispatch } = this.props;
    const that = this;
    form.validateFields((err, values) => {
      if (!err) {
        that.AddModal.loading();
        const payload = {
          ...values,
          category: 1,
        };
        // this.getTopoData(form);
        dispatch({
          type: "fireWall/addEquipment",
          payload: payload,
          callback: (res) => {
            that.AddModal.unLoading();
            that.AddModal.onHide();
            if (res.code == 0) {
              message.success("新增成功");
              this.getDeviceList();
              // that.addDeviceNode(values);
            } else {
              message.error(res.msg);
            }
          },
        });
      }
    });
  };







  render() {

    const {
      restoreList,
      currentPage,    
      visiableRestore,
      loading
    } = this.state;
    const { getFieldDecorator } = this.props.form;

    return (
      <Fragment>
        <div style={{ marginTop: 23, marginLeft: 30, marginRight: 30 }}>
          <Card
            style={{
              borderRadius: 8,
            }}
          >
            <Form
              className="searchBg"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 14 }}
            >
              <Row>
                <Col span={5}>
                  <FormItem
                    // labelCol={{ span: 6 }}
                    // wrapperCol={{ span: 14 }}
                    label="设备名称:"
                  >
                    {getFieldDecorator("deviceName", {
                      rules: [
                        {
                          pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,
                          message: "请输入汉字、字母、数字、短横线或下划线",
                        },
                      ],
                    })(<Input maxLength={50} placeholder="设备名称" />)}
                  </FormItem>
                </Col>              
                <Col span={5}>
                  <FormItem label="设备IP:">
                    {getFieldDecorator("deviceIp", {
                      rules: [
                        {
                          pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_\.]*$/,
                          message: "请输入汉字、字母、点、数字、短横线或下划线",
                        },
                      ],
                    })(<Input maxLength={50} placeholder="IP" />)}
                  </FormItem>
                </Col>              
                <Col span={4} className="searchBtn">
                  <Button type="primary" onClick={this.handleAdvancedSearch}>
                    查询
                  </Button>
                  <Button style={{ marginLeft: 15 }} onClick={this.handleReset}>
                    清除
                  </Button>
                </Col>
              </Row>
            </Form>
          </Card>
          <div style={{ marginBottom: 20, marginTop: 20 }}>
            <Button
              type="primary"
              style={{ marginRight: 15, borderRadius: 2 }}
              onClick={this.handleAddClick}
            >
              新建备份
            </Button>
            {/* <Button
              style={{ borderRadius: 2 }}
              onClick={this.handleDeleteClick}
            >
              批量删除
            </Button> */}
          </div>
          <Card
            bordered={false}
            style={{
              borderRadius: 8,
            }}
            className="TableContainer"
          >
            <Table
              className={styles.tablBar}
              // rowSelection={rowSelection}
              dataSource={restoreList.rows}
              loading={loading}
              // scroll={{ x: 1500 }}
              rowKey={(record) => record.id}
              pagination={false}
              locale={{
                emptyText: "暂无数据信息",
              }}
            >
              <Column
                title="序号"
                dataIndex="key"
                width={50}
                // fixed="left"
                key="key"
                render={(text, record, index) => {
                  return `${index + 1}`;
                }} 
              />
              <Column
                title="设备名称"
                dataIndex="deviceName"
                width={200}
                // fixed="left"
                key="deviceName"
              />
                <Column
                title="设备IP"
                dataIndex="deviceIp"
                width={120}
                key="deviceIp"
              />                                       
              <Column
                title="最后一次备份时间"
                dataIndex="lastBackUpTime"
                width={180}
                key="lastBackUpTime"
                render={
                  (text, record, index) => {
                      return `${moment(record.lastBackUpTime).format('YYYY-MM-DD HH:mm')}`
                  }
              }
              />

              <Column
                title="操作"
                dataIndex="action"
                key="action"
                align= 'left'
                width={120}
                render={(text, record) => (
                  <div style={{ color: primaryColor }}>             
                      <a
                        style={{ borderRadius: 2, color: primaryColor }}
                        onClick={() => {
                          this.handleRestore(record);
                        }}
                      >
                        还原
                      </a>           
                  </div>
                )}
              />
            </Table>
            <Row type="flex" justify="end" style={{ marginTop: 20 }}>
              <Pagination
                current={currentPage}
                showQuickJumper
                showSizeChanger
                onShowSizeChange={this.onShowSizeChange}
                total={restoreList.total && parseInt(restoreList.total)}
                showTotal={this.showTotal}
                onChange={this.handlePageChange}
              />
            </Row>
          </Card>

         {visiableRestore&&<AddDeviceModal
            handleAddCancel={this.handleAddCancel}
            visiableRestore={visiableRestore}
            getDeviceList={this.getDeviceList}
          />} 
        </div>
      </Fragment>
    );
  }
}
