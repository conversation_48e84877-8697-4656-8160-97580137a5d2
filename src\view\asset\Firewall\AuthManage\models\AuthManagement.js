import {
	authorizeSearch,
	addAuthorize,
	authorizeCheck,
	addReAuth
	} from '@/services/AuthManagement/authManagement';

export default {
	namespace: 'authManagement',

	state: {

	},

	effects: {
		*authorizeSearchData({payload,callback},{call,put}){
			try {
				const response = yield call(authorizeSearch, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*authorizeAddData({payload,callback},{call,put}){
			try {
				const response = yield call(addAuthorize, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*authorizeReAddData({payload,callback},{call,put}){
			try {
				const response = yield call(addReAuth, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*authorizeCheckData({payload,callback},{call,put}){
			try {
				const response = yield call(authorizeCheck, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
	},

	reducers: {
	
	}
}