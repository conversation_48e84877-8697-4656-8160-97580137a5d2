{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\AuthManage\\components\\DeviceSyncComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\AuthManage\\components\\DeviceSyncComponent.vue", "mtime": 1750123973395}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}