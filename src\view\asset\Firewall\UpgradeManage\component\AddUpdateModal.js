import React, { Component, Fragment } from "react";
import { Modal, Form, TreeSelect, Button, Upload, Icon,message,Select } from "antd";
import { connect } from "dva";
const FormItem = Form.Item;
const { TreeNode } = TreeSelect;
@Form.create()
@connect(({}) => ({}))
export default class AddUpdateModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: this.props.visiableAddUpdate || false,
      value: 1,
      loading: false,
      showTitle: false,
      deviceData:[],  //所有分组和设备集合
      deviceArr:[],   //被选中的数量id集合
      fileAttList:[],
      file:{},
      modalLoading:false 
    };
  }
  componentDidMount() {
    this.getGroupList();
  }

  /**
   * 设备
   * @param
   */
  getGroupList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: "inspectionManagement/addInspectionData",
      payload: {},
      callback: (res) => {
        if (res.retcode == 0) {
          this.setState({
            deviceData: res.data,
          });
        } else {
          message.error(res.msg);
        }
      },
    });
  };

  renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.childList) {
        return (
          <TreeNode title={item.name} key={item.compId}  disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId}>
            {this.renderTreeNodes(item.childList)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.name} key={item.compId}  disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId} />;
    });
  };
  onChange = (e) => {
    if (e.target.value == 2) {
      this.setState({});
    } else {
      this.setState({});
    }
    this.setState({
      value: e.target.value,
    });
  };

  renderHeader = () => (
    <div className="modalTitle">
      <h4>新建</h4>
    </div>
  );


  handleCancelClick = () => {
    this.props.handleAddCancel();
  };

  //  设备选中
  handleTreeValue = (value, label, extra) => {
    let deviceArr 
    if(value.substring(0,1)=='1'){
      deviceArr = value.split(',')[2]
    }
   this.setState({
    deviceArr
   })
  };
  // 保存
  handleSave =()=>{
    const { dispatch, form  } = this.props;
    const { deviceArr, inspectionType } = this.state;
    if(this.props.type=='1'){
      form.validateFields((err, values) => {
        if (!err) {
          this.setState({
            modalLoading:true
          })
          dispatch({
            type: "upgrade/addVirusData",
            payload:{
              deviceIds:deviceArr,
              fileName:file.name
            },
            callback:(res)=> {
              if (res.retcode == 0) {
                message.success("添加成功!");
                this.handleCancelClick();
                this.props.handleVirus();
              } else {
                message.error(res.msg);
              }
              this.setState({
                modalLoading:false
              })
            },
          });
        }
      });
    }else{    
      form.validateFields((err, values) => {
        if (!err) {
          this.setState({
            modalLoading:true
          })
          dispatch({
            type: "upgrade/addVersionData",
            payload:{
              deviceIds:deviceArr,
              fileName:file.name
            },
            callback:(res)=> {
              if (res.retcode == 0) {
                message.success("添加成功!");
                this.handleCancelClick();
                this.props.getUpdateList();
              } else {
                message.error(res.msg);
              }
              this.setState({
                modalLoading:false
              })
            },
          });
        }
      });
    }
  };
  propsApi = {
    name: 'file',
    action: this.props.type=='1'?'/ap/v1/featLibUpgrade/move':'/ap/v1/upgrade/move',
    headers: {
      authorization: 'authorization-text',
    },
    onChange:(info)=> {
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done') {
        this.setState({
         file:info.file
        })
        message.success(`${info.file.name} 文件上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
  };
 

  render() {
    const { visible, loading, deviceData,modalLoading } = this.state;
    const {
      form: { getFieldDecorator },
      saveOnClick,
    } = this.props;
    const props = {};
    return <Fragment>
        <Modal 
            visible={visible} 
            title={this.renderHeader()} 
            destroyOnClose 
            centered 
            width={650} 
            onCancel={this.handleCancelClick} 
            maskClosable={false}
            footer={[<Button key="back" onClick={this.handleCancelClick}>
              取消
            </Button>, <Button key="submit" type="primary"   loading={modalLoading} onClick={this.handleSave}>
              确认
            </Button>]}>
          <Form>
            <FormItem labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} label="选择设备：">
              {getFieldDecorator("deviceIds", {
                rules: [
                  {
                    required: true,
                    message: "请选择设备",
                  },
                ],
                // initialValue:''
              })(<TreeSelect  onChange={this.handleTreeValue} style={{ width: "100%" }} dropdownStyle={{ maxHeight: 400, overflow: "auto" }} placeholder="请选择设备">
                  {this.renderTreeNodes(deviceData)}
                </TreeSelect>)}
            </FormItem>
            {this.props.type == 1 && <FormItem labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} label="特征库：">
                {getFieldDecorator("updateCategory", {                  
                  initialValue: "IPS",
                  rules: [
                    {
                      required: true,
                      message: "请选择特征库",
                    },
                  ],
                })(<Select>
                    <Option value="IPS">IPS</Option>
                    <Option value="AV">AV</Option>
                  </Select>)}
              </FormItem>}
            <Form.Item label="选择授权文件:" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              {getFieldDecorator("file", {
                rules: [
                  {
                    required: true,
                    message: "选择授权文件",
                  },
                ],
                // initialValue:''
              })(
              <Upload {...this.propsApi}>
                  <Button>
                    <Icon type="upload" /> 导入
                  </Button>
                </Upload>
                )}
            </Form.Item>
          </Form>
        </Modal>
      </Fragment>;
  }
}
