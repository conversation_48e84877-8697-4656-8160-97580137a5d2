import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";
//工业协议集-设备集合
export async function addInspectionData(params) {
  return request(`/dev/device/all`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//工业协议集-新增接口
export async function industryProtocolAdd(params) {
  return request(`/dev/industryProtocol/add`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//工业协议集-编辑接口
export async function industryProtocolUpdate(params) {
    return request(`/dev/industryProtocol/update`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      data: JSON.stringify(params),
    });
  }
//工业协议集-查询接口
export async function industryProtocolSearch(params) {
  return request(`/dev/industryProtocol/pages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//工业协议集-查看
export async function industryProtocolInfo(params) {
    return request(`/dev/industryProtocol/infor`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      data: JSON.stringify(params),
    });
  }
//工业协议集-删除接口
export async function industryProtocolDelete(params) {
  return request(`/dev/industryProtocol/delete`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//工业协议集-批量下发
export async function protocolIssued(params) {
    return request(`/dev/industryProtocol/protocolIssued`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      data: JSON.stringify(params),
    });
  }
//工业协议集-同步设备协议
export async function syncFromDevice(params) {
    return request(`/dev/industryProtocol/syncFromDevice`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      data: JSON.stringify(params),
    });
  }
 //工业协议集-切换协议
export async function functionCode(params) {
  return request(`/dev/functionCode/list?${stringify(params)}`, {
    method: 'GET',
  });
}