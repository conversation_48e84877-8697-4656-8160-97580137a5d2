<template>
  <el-dialog
    title="新建授权"
    :visible.sync="dialogVisible"
    width="650px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="选择设备" prop="deviceId">
        <el-tree-select
          v-model="formData.deviceId"
          :data="deviceData"
          :props="treeProps"
          placeholder="请选择设备"
          style="width: 100%"
          :check-strictly="true"
          @change="handleDeviceChange"
        />
      </el-form-item>

      <el-form-item label="许可证类型" prop="licenceCategory">
        <el-select v-model="formData.licenceCategory" placeholder="请选择许可证类型">
          <el-option label="IPS" value="IPS"></el-option>
          <el-option label="AntiVirus" value="AntiVirus"></el-option>
          <el-option label="SOFT" value="SOFT"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="选择授权文件" prop="file">
        <el-upload
          ref="upload"
          :action="uploadAction"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :auto-upload="true"
          accept=".lic,.license"
        >
          <el-button>
            <i class="el-icon-upload"></i>
            导入
          </el-button>
          <div slot="tip" class="el-upload__tip">只能上传 .lic/.license 文件</div>
        </el-upload>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addAuth, getDeviceList } from '@/api/firewall/authManagement'

export default {
  name: 'AddAuthModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    typeAuth: {
      type: Number,
      default: 0, // 0: 新增, 1: 重新授权
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      formData: {
        deviceId: '',
        licenceCategory: 'IPS',
        file: null,
      },
      rules: {
        deviceId: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
        licenceCategory: [
          { required: true, message: '请选择许可证类型', trigger: 'change' },
        ],
        file: [
          { required: true, message: '请选择授权文件', trigger: 'change' },
        ],
      },
      deviceData: [],
      fileList: [],
      uploadedFile: null,
      treeProps: {
        children: 'childList',
        label: 'name',
        value: 'srcId',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
      uploadAction: '/ap/v1/licenseManage/move',
      uploadHeaders: {
        authorization: 'authorization-text',
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadDeviceData()
      }
    },
  },
  methods: {
    initForm() {
      this.formData = {
        deviceId: '',
        licenceCategory: 'IPS',
        file: null,
      }
      this.fileList = []
      this.uploadedFile = null
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          value: item.srcId,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleDeviceChange(value) {
      // 设备选择变化处理
    },
    beforeUpload(file) {
      const isValidType = file.name.endsWith('.lic') || file.name.endsWith('.license')
      if (!isValidType) {
        this.$message.error('只能上传 .lic 或 .license 格式的文件!')
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      return true
    },
    handleUploadSuccess(response, file) {
      this.uploadedFile = file
      this.formData.file = file.name
      this.$message.success(`${file.name} 文件上传成功`)
      // 手动触发表单验证
      this.$refs.form.validateField('file')
    },
    handleUploadError(error, file) {
      this.$message.error(`${file.name} 文件上传失败`)
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const params = {
              licenceCategory: this.formData.licenceCategory,
              deviceIds: this.formData.deviceId,
              fileName: this.formData.file,
            }
            
            const res = await addAuth(params)
            if (res.retcode === 0) {
              this.$message.success('添加成功!')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

:deep(.el-upload__tip) {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
