<!--
 * @Author: 光影
 * @Date: 2024-11-14 10:57:02
 * @LastEditors: 光影
 * @LastEditTime: 2024-11-26 10:32:06
 * @FilePath: /smp/src/view/visualization/overview/overview.vue
 * @Description: 风险态势概览
-->
<template>
  <el-container ref="visualizationOverviewWrap">
    <el-main class="overview-main-wrap">
      <el-backtop target=".overview-main-wrap"></el-backtop>
      <VisualizationBanner ref="visualizationBannerRef" @timeChange="timeChange" @refreshTime="refreshTimeChange" />
      <div class="growth-chart">
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="16">
            <secondary-auth title="二次授权(待审)" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="拒绝高危访问 TOP5" :num="3" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="区域上报高危事件 TOP5" :num="1" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="哨兵上报高危事件 TOP5" :num="2" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="拒绝高危访问 TOP5" :num="3" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="哨兵上报高危事件源IP TOP5" :num="4" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="哨兵上报高危事件目的IP TOP5" :num="5" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="识别病毒文件 TOP5" :num="6" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="风险操作涉事人员 TOP5" :num="7" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="病毒传输涉事人员 TOP5" :num="8" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <PieChart title="运维类型占比" :num="9" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <PieChart title="威胁情报命中类型占比" :num="10" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="入侵攻击类型 TOP5" :num="11" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="策略配置偏离基线 TOP5" :num="12" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="高发传统风险操作 TOP5" :num="13" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="高发工控风险操作 TOP5" :num="14" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <TrendChart title="监控告警趋势" :num="15" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="违规外联方式占比" :num="16" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="高危端口高频访问 TOP5" :num="17" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <PieChart title="哨兵系统版本分布" :num="18" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="违反地址绑定的MAC TOP5" :num="19" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <PieChart title="文件类型事件占比" :num="20" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <Table title="各区域哨兵设备部署模式" :num="21" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="发现违反工控基线的哨兵 TOP5" :num="22" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <PieChart title="病毒传播途径占比" :num="23" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <TrendChart title="接入日志趋势" :num="24" />
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <HorizonalBar title="发现串口工控事件的哨兵 TOP5" :num="25" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8">
            <TrendChart title="识别关联事件趋势" :num="26" />
          </el-col>
          <el-col class="lg-mb-20" :xs="24" :sm="24" :md="24" :lg="8"></el-col>
          <PlatformInfo title="工控哨兵管理平台系统信息" :num="27" />
        </el-row>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import VisualizationBanner from './components/VisualizationBanner'
import PieChart from './components/PieChart'
import TrendChart from './components/TrendChart'
import Table from './components/Table'
import HorizonalBar from './components/HorizonalBar'
import PlatformInfo from './components/PlatformInfo'
import SecondaryAuth from './components/SecondaryAuth'

import { closest, addClass, removeClass } from '@util/dom'
import { IntervalTimer } from '@util/date'

export default {
  name: 'VisualizationOverview',
  components: {
    VisualizationBanner,
    PieChart,
    TrendChart,
    Table,
    HorizonalBar,
    PlatformInfo,
    SecondaryAuth,
  },
  provide() {
    return {
      shareState: this.shareState,
    }
  },
  data() {
    return {
      shareState: {
        loadTimes: 0,
        timeValue: 4,
      },
      timer: null,
    }
  },
  mounted() {
    this.timer = new IntervalTimer(this.refreshData, 1 * 60000)
    this.transWrapCss(true)
    // 开始定时器
    this.timer.start()
    // 默认刷新一次
    this.refreshData()
  },
  beforeDestroy() {
    this.transWrapCss(false)
    this.timer.stop()
  },
  methods: {
    refreshData() {
      this.shareState.loadTimes = this.shareState.loadTimes + 1
    },
    refreshTimeChange(time) {
      if (time === 0) {
        this.refreshData()
      } else {
        this.timer.stop()
        this.timer = new IntervalTimer(this.refreshData, time * 60000)
        this.timer.start()
      }
    },
    timeChange(val) {
      this.shareState.timeValue = val
      this.refreshData()
    },
    transWrapCss(flag) {
      const routerContainerDom = closest(this.$refs.visualizationOverviewWrap.$el, '.router-container')
      if (routerContainerDom) {
        flag === true ? addClass(routerContainerDom, 'no-bg') : removeClass(routerContainerDom, 'no-bg')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep.el-main {
  padding: 0px;
  box-sizing: border-box;
  overflow-x: hidden;
}
::v-deep.el-header {
  padding: 0px;
  box-sizing: border-box;
  &.header-wrapper {
    text-align: right;
    height: 40px;
    padding-top: 6px;
  }
}

.growth-chart {
  margin-top: 0px;
}
</style>
