{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue?vue&type=template&id=170d4f3c&scoped=true&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue", "mtime": 1749799266715}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745219693993}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}