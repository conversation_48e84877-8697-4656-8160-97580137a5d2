import React, { Component, Fragment } from "react";
import { Modal, Form, TreeSelect, Button, Upload, Icon } from "antd";
import { connect } from "dva";
const FormItem = Form.Item;
const { TreeNode } = TreeSelect;
@Form.create()
@connect(({}) => ({}))
export default class AuthDevice extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: this.props.visiableAuth || false,
      value: 1,
      loading: false,
    };
  }
  componentDidMount() {
  }

  



  renderHeader = () => (
    <div className="modalTitle">
      <h4>新建授权</h4>
    </div>
  );


  handleCancelClick = () => {
    this.props.handleAddCancel();
  };


  // 保存
  handleSave =()=>{
    const { dispatch, form } = this.props;
    const { deviceArr, inspectionType } = this.state;
    form.validateFields((err, values) => {
      if (!err) {
        dispatch({
          type: "authManagement/authorizeAddData",
          payload:{},
          callback:(res)=> {
            if (res.retcode == 0) {
              message.success("添加成功!");
              // this.handleCancelClick();
              // this.props.getInspectionData();
            } else {
              message.error(res.message);
            }
          },
        });
      }
    });
  };
  render() {
    const { visible, loading, deviceData } = this.state;
    const {
      form: { getFieldDecorator },
      saveOnClick,
    } = this.props;
    const props = {};
    return (
      <Fragment>
        <Modal
          visible={visible}
          title={this.renderHeader()}
          destroyOnClose
          centered
          width={650}
          onCancel={this.handleCancelClick}
          maskClosable={false}
          footer={[
            <Button key="back" onClick={this.handleCancelClick}>
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={this.handleSave}
            >
              确认
            </Button>,
          ]}
        >
          <Form>           
            <Form.Item
              label="选择授权文件:"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
            >
              {getFieldDecorator("01", {
                rules: [
                  {
                    required: true,
                    message: "选择授权文件",
                  },
                ],
                // initialValue:''
              })(
                <Upload {...props}>
                  <Button>
                    <Icon type="upload" /> 导入
                  </Button>
                </Upload>
              )}
            </Form.Item>
          </Form>
        </Modal>
      </Fragment>
    );
  }
}
