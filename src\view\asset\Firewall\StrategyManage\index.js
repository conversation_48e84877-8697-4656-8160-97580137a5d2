import React, { useState, useEffect, createRef } from "react";
import { message, Modal, Button, Form, Row, Col, Input } from "antd";
import moment from "moment";
import SbrTable from "@/components/SbrTable";
import DeviceComponent from "./components/deviceComponent";
import AddProtocol from "./components/addProtocol";
import Application from './components/application';
import View from './components/view';
import { tacticsSearch, tacticsDelete, protocolIssued } from "./services";
import styles from "./index.less";
const tableRef = createRef();
const deviceRef = createRef();
const AddProtocolRef = createRef();
const viewRef = createRef();
const applicationRef = createRef();
const List = (props) => {
  const { getFieldDecorator, resetFields, validateFields } = props.form;

  // 列表数据
  const [tableList, setTableList] = useState([]);
  //  设备组件传参类型
  const [type, setType] = useState('');
  // 查询条件
  const [queryValue, setQueryValue] = useState({});
  const [loading, setLoading] = useState(false);
  // 编辑数据
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  useEffect(
    () => {
      getSourceData(true);
    },
    [queryValue]
  );

  // 查询列表
  const getSourceData = async (isSearch=false) => {
    try {
      setLoading(true);
      setSelectedRowKeys([]);
      const res = await tacticsSearch(isSearch?{
        pageIndex: 1,
        pageSize: 10,
        ...queryValue,
      }:{
        ...tableRef?.current?.getValue(),
        ...queryValue,
      });
      if (res.retcode == 0) {
        setTableList(res.data);
        setLoading(false);
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  };
  // 删除
  const deleteProtocol = (record={}) => {
      Modal.confirm({
        title: "删除",
        content: "确定要删除选中协议吗?删除后不可恢复",
        centered: true,
        okText: "确认",
        cancelText: "取消",
        onOk: async () => {
          try {
            const res = await tacticsDelete({ ids: record.id });
            if (res.retcode == 0) {
              message.success("删除成功");
              getSourceData();
            } else {
              message.error(res.msg);
            }
          } catch (err) {
            console.log(err);
          }
        },
      });
  };
    //  协议下发
    const handleIssue = (record={}) => {
        setType('1')
        deviceRef.current.showDrawer(record,  [record.id]);
       
    };
    //  启停
    const handleChange = (record={}) => {
      setType('3')
      deviceRef.current.showDrawer(record,  [record.id]);      
  };
    // 批量删除
    const batchDeleteProtocol = (record={}) => {
        if (selectedRowKeys.length) {
          Modal.confirm({
            title: "删除",
            content: "确定要删除选中协议吗?删除后不可恢复",
            centered: true,
            okText: "确认",
            cancelText: "取消",
            onOk: async () => {
              try {
                const res = await tacticsDelete({ ids: selectedRowKeys.join(',') });
                if (res.retcode == 0) {
                  message.success("删除成功");                
                  getSourceData();
                } else {
                  message.error(res.msg);
                }
              } catch (err) {
                console.log(err);
              }
            },
          });
        } else {
          message.error("至少选中一条数据");
        }
    };
  // 列表数据
  const columns = [
    {
      title: "序号",
      key: "key",
      dataIndex: "key",
      width: 50,
      render: (text, record, index) => {
        return `${index + 1}`;
      },
    },
    {
      title: "名称",
      key: "name",
      dataIndex: "name",
      ellipsis: true,
      render: (text, record) => {
        return (
          <>
            <a
              onClick={() => {
                view(record);
              }}
            >
              {record.name}
            </a>
          </>
        );
      },
    },
    {
      title: "来源设备",
      key: "srcDeviceName",
      dataIndex: "srcDeviceName",
      ellipsis: true,
    },
    {
      title: "来源ip",
      key: "srcIp",
      dataIndex: "srcIp",
      width: 100,
    },
    {
      title: "规则类型",
      key: "ruleType",
      width: 120,
      dataIndex: "ruleType",
      render: (text, record) => {
        return record.ruleType == "1" ? "工业协议" : "服务集";
      },
    },
    {
      title: "访问控制",
      key: "permitType",
      dataIndex: "permitType",
      width: 90,
      render: (text, record) => {
        return record.permitType == "permit" ? "允许" : "拒绝";
      },
    },
    {
      title: "应用防护",
      key: "isOpenAppProtect",
      dataIndex: "isOpenAppProtect",
      width: 90,
      render: (text, record) => {
        return record.isOpenAppProtect == "0" ? "启用" : "禁用";
      },
    },
    // {
    //   title: "下发状态",
    //   key: "status",
    //   dataIndex: "status",
    //   render: (text, record) => (
    //     <>
    //     <span
    //       className={
    //         record.status == '0'
    //           ? 'redColor'
    //           : 'blueColor'                                    
    //       }
    //     >          
    //       {record.status == "0" ? "下发失败" : "下发成功"}
    //     </span>
    //   </>
    //   )
    // },
    {
      title: "应用范围",
      key: "categeory",
      dataIndex: "cateegory",
      width: 90,
      render: (text, record) => {
        return (
          <>
            <a
              onClick={() => {
                application(record);
              }}
            >
             查看
            </a>
          </>
        );
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      render: (text, record) => (
        <>
          <div className="table-option">           
            <a
              onClick={() => {
                handleAdd(record);
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                deleteProtocol(record);
              }}
            >
              删除
            </a>
            <a
              onClick={() => {
                handleChange(record);
              }}
            >
              启停
            </a>
            <a
              onClick={() => {
                handleIssue(record);
              }}
            >
              策略下发
            </a>
          </div>
        </>
      ),
    },
  ];
   // 应用范围
 const application = (record = {}) => {
  applicationRef.current.showDrawer(record);
};
 // 查看
 const view = (record = {}) => {
    viewRef.current.showDrawer(record);
  };

  // 新增
  const handleAdd = (record = {}) => {
    AddProtocolRef.current.showDrawer(record);
  };

  // 批量下发
  const batchIssue = (record = {}, copy) => {
    setType('1')
    if (selectedRowKeys.length) {
        deviceRef.current.showDrawer(record, selectedRowKeys);
      } else {
        message.error("至少选中一条数据");
      }
   
  };
  // 同步设备协议
  const synProtocol = (record = {}, copy) => {
    setType('2')
    // if (selectedRowKeys.length) {
    //     deviceRef.current.showDrawer(record, selectedRowKeys);
    //   } else {
    //     message.error("至少选中一条数据");
    //   }
    deviceRef.current.showDrawer(record, []);
  };

  // 条件查询
  const handleSearch = (e) => {
    e.preventDefault();
   validateFields((err, values) => {
        if (!err) {
            setQueryValue(values);
        }
      });
  };

  // 条件清除
  const handleReset = () => {
    resetFields();
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };
  return (
    <div>
      <Form
        className="searchBg"
        onSubmit={handleSearch}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        // form={form}
      >
        <Row>
          <Col span={8}>
            <Form.Item label="来源设备">
              {getFieldDecorator("name", {})(
                <Input maxLength={50} placeholder="请输入名称" />
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="来源ip">
              {getFieldDecorator("ip", {})(
                <Input maxLength={50} placeholder="请输入ip" />
              )}
            </Form.Item>
          </Col>
          <Col span={8} className="searchBtn">
            <Button type="primary" htmlType="submit">
              查询
            </Button>
            <Button style={{ marginLeft: 15 }} onClick={handleReset}>
              清除
            </Button>
          </Col>
        </Row>
      </Form>
      <div style={{ marginBottom: 20, marginTop: 20 }}>
        <Button
          type="primary"
          style={{ marginRight: 15, borderRadius: 2 }}
          onClick={() => {
            handleAdd();
          }}
        >
          新建策略资源
        </Button>
        <Button
          type="primary"
          style={{ marginRight: 15, borderRadius: 2 }}
          onClick={()=>{batchIssue()}}
        >
          批量下发
        </Button>
        <Button
          type="primary"
          style={{ marginRight: 15, borderRadius: 2 }}
          onClick={() => {synProtocol()}}
        >
          同步设备策略
        </Button>
        <Button style={{ borderRadius: 2 }} onClick={()=>{batchDeleteProtocol()}}>
          批量删除
        </Button>
      </div>
      <div className={`tableBg`}>
        <SbrTable
          columns={columns}
          scroll={false}
          tableList={tableList}
          getSourceData={getSourceData}
          style={{ wordWrap: "break-word", wordBreak: "break-all" }}
          rowKey={(record) => record.id}
          //   size="middle"
          ref={tableRef}
          loading={loading}
          rowSelection={rowSelection}
        />
      </div>
      <DeviceComponent ref={deviceRef} getSourceData={getSourceData} typeButton={type}/>
      <AddProtocol ref={AddProtocolRef} getSourceData={getSourceData} />
      <View ref={viewRef} />
      <Application ref={applicationRef} />      
    </div>
  );
};

export default Form.create()(List);
