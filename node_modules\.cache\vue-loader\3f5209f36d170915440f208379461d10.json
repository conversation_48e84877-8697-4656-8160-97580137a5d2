{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\AddSentineUpdateModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\AddSentineUpdateModal.vue", "mtime": 1749800681690}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}