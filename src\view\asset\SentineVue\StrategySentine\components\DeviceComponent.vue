<template>
  <el-drawer
    title="策略下发"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="设备名称:">
          <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备IP:">
          <el-input v-model="searchForm.deviceIp" placeholder="请输入设备IP" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="device-list">
        <el-table
          :data="deviceList"
          v-loading="loading"
          height="400"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="设备名称"></el-table-column>
          <el-table-column prop="ip" label="设备IP"></el-table-column>
          <el-table-column prop="status" label="在线状态">
            <template slot-scope="scope">
              <span :class="scope.row.status === 1 ? 'status-online' : 'status-offline'">
                {{ scope.row.status === 1 ? '在线' : '离线' }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="distributeLoading" @click="handleDistribute">下发</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getSentineDeviceList } from '@/api/sentine/deviceManagement'
import { distributeStrategy } from '@/api/sentine/strategyManagement'

export default {
  name: 'DeviceComponent',
  props: {
    category: {
      type: Number,
      default: 4,
    },
    typeButton: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      drawerVisible: false,
      loading: false,
      distributeLoading: false,
      searchForm: {
        deviceName: '',
        deviceIp: '',
      },
      deviceList: [],
      selectedDevices: [],
      currentRecord: null,
      strategyIds: [],
    }
  },
  methods: {
    showDrawer(record = {}, strategyIds = []) {
      this.drawerVisible = true
      this.currentRecord = record
      this.strategyIds = strategyIds
      this.getDeviceList()
    },
    handleClose() {
      this.drawerVisible = false
      this.resetData()
    },
    resetData() {
      this.searchForm = {
        deviceName: '',
        deviceIp: '',
      }
      this.deviceList = []
      this.selectedDevices = []
      this.currentRecord = null
      this.strategyIds = []
    },
    async getDeviceList() {
      this.loading = true
      const payload = {
        _limit: 100,
        _page: 1,
        queryParams: {
          fireName: this.searchForm.deviceName,
          originIp: this.searchForm.deviceIp,
        },
        type: this.category,
      }

      try {
        const res = await getSentineDeviceList(payload)
        if (res.retcode === 0) {
          this.deviceList = res.data.items || []
        } else {
          this.$message.error(res.message || '获取设备列表失败')
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.getDeviceList()
    },
    handleResetSearch() {
      this.searchForm = {
        deviceName: '',
        deviceIp: '',
      }
      this.getDeviceList()
    },
    handleSelectionChange(selection) {
      this.selectedDevices = selection
    },
    async handleDistribute() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning('请选择要下发的设备')
        return
      }

      this.distributeLoading = true
      const deviceIds = this.selectedDevices.map(device => device.id)

      try {
        // 调用策略下发API
        const res = await distributeStrategy({
          strategyIds: this.strategyIds,
          deviceIds: deviceIds,
        })

        if (res.retcode === 0) {
          this.$message.success('策略下发成功')
          this.$emit('on-submit')
          this.handleClose()
        } else {
          this.$message.error(res.msg || '策略下发失败')
        }
      } catch (err) {
        console.log(err)
        this.$message.error('策略下发失败')
      } finally {
        this.distributeLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-form {
  margin-bottom: 20px;
}

.device-list {
  flex: 1;
  overflow: hidden;
}

.drawer-footer {
  margin-top: 20px;
  text-align: right;
  border-top: 1px solid #e8e8e8;
  padding-top: 20px;
}

.status-online {
  color: #67c23a;
}

.status-offline {
  color: #f56c6c;
}
</style>
