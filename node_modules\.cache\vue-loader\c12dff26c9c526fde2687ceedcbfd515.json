{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\index.vue?vue&type=template&id=1789f6af&scoped=true&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\index.vue", "mtime": 1750059734500}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745219693993}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}