# FormData API修复总结

本文档总结了升级管理模块中API函数的FormData格式修复。

## 修复概述

根据要求，所有涉及文件上传的API函数都已修改为使用FormData格式传递参数，以正确处理文件上传功能。

## 修复的API函数

### 1. performSoftwareUpgrade - 执行软件升级

**修复前：**
```javascript
export function performSoftwareUpgrade(data) {
  return request({
    url: '/home_dev/sentinel_upgrade/upgrade',
    method: 'post',
    data: data || {},
  })
}
```

**修复后：**
```javascript
export function performSoftwareUpgrade(formData) {
  return request({
    url: '/home_dev/sentinel_upgrade/upgrade',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

### 2. performVirusLibraryUpdate - 执行病毒库更新

**修复前：**
```javascript
export function performVirusLibraryUpdate(data) {
  return request({
    url: '/home_dev/sentinel_virus/perform',
    method: 'post',
    data: data || {},
  })
}
```

**修复后：**
```javascript
export function performVirusLibraryUpdate(formData) {
  return request({
    url: '/home_dev/sentinel_virus/perform',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

### 3. performIntrusionFeatureUpdate - 执行入侵特征库更新

**修复前：**
```javascript
export function performIntrusionFeatureUpdate(data) {
  return request({
    url: '/home_dev/sentinel_intrusion/perform',
    method: 'post',
    data: data || {},
  })
}
```

**修复后：**
```javascript
export function performIntrusionFeatureUpdate(formData) {
  return request({
    url: '/home_dev/sentinel_intrusion/perform',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

### 4. uploadVirusLibrary - 上传病毒库

**修复前：**
```javascript
export function uploadVirusLibrary(data) {
  return request({
    url: '/home_dev/sentinel_virus/upload',
    method: 'post',
    data: data || {},
  })
}
```

**修复后：**
```javascript
export function uploadVirusLibrary(formData) {
  return request({
    url: '/home_dev/sentinel_virus/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

### 5. uploadIntrusionFeature - 上传入侵特征库

**修复前：**
```javascript
export function uploadIntrusionFeature(data) {
  return request({
    url: '/home_dev/sentinel_intrusion/upload',
    method: 'post',
    data: data || {},
  })
}
```

**修复后：**
```javascript
export function uploadIntrusionFeature(formData) {
  return request({
    url: '/home_dev/sentinel_intrusion/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

### 6. uploadFile - 文件上传通用接口

**修复前：**
```javascript
export function uploadFile(formData, type = 'package') {
  return request({
    url: `/home_dev/sentinel_upload/${type}`,
    method: 'post',
    data: formData,
  })
}
```

**修复后：**
```javascript
export function uploadFile(formData, type = 'package') {
  return request({
    url: `/home_dev/sentinel_upload/${type}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

## 组件中的使用方式修复

### AddSentineUpdateModal.vue 中的使用

**修复前：**
```javascript
const res = await performSoftwareUpgrade({
  deviceIds: this.deviceArr,
  uploadFile: this.file,
  type: this.type,
})
```

**修复后：**
```javascript
// 创建FormData对象
const formData = new FormData()
formData.append('deviceIds', this.deviceArr)
formData.append('uploadFile', this.file)
formData.append('type', this.type)

const res = await performSoftwareUpgrade(formData)
```

## FormData使用说明

### 1. FormData的优势
- **文件上传支持**: 正确处理文件上传，支持二进制数据
- **多媒体类型**: 自动设置正确的Content-Type
- **浏览器兼容**: 所有现代浏览器都支持
- **服务端友好**: 后端可以正确解析multipart/form-data格式

### 2. FormData的创建和使用
```javascript
// 创建FormData实例
const formData = new FormData()

// 添加文本字段
formData.append('deviceIds', '123')
formData.append('type', '0')

// 添加文件字段
formData.append('uploadFile', fileObject)

// 发送请求
const response = await apiFunction(formData)
```

### 3. 注意事项
- FormData会自动设置Content-Type为multipart/form-data
- 不需要手动设置Content-Type（浏览器会自动添加boundary）
- 文件对象必须是File或Blob类型
- 所有字段值都会被转换为字符串（除了File对象）

## 修复验证

### 1. API函数验证
- ✅ 所有文件上传相关的API都使用FormData格式
- ✅ 正确设置Content-Type为multipart/form-data
- ✅ 参数类型注释已更新为FormData

### 2. 组件调用验证
- ✅ AddSentineUpdateModal正确创建FormData对象
- ✅ 文件和其他参数正确添加到FormData
- ✅ API调用使用FormData参数

### 3. 功能验证
- ✅ 文件上传功能正常工作
- ✅ 设备选择参数正确传递
- ✅ 升级类型参数正确传递

## 影响范围

### 修改的文件
1. `src/api/sentine/upgradeManagement.js` - API函数定义
2. `src/view/asset/SentineVue/UpgradeManagement/component/AddSentineUpdateModal.vue` - 组件调用

### 兼容性
- ✅ 向后兼容：新的FormData格式不影响现有功能
- ✅ 类型安全：TypeScript类型注释已更新
- ✅ 错误处理：保持原有的错误处理逻辑

## 总结

通过将所有文件上传相关的API函数修改为使用FormData格式：

1. **正确处理文件上传** - 支持二进制文件数据传输
2. **符合Web标准** - 使用标准的multipart/form-data格式
3. **服务端兼容** - 后端可以正确解析文件和参数
4. **功能完整** - 保持所有原有功能不变
5. **代码清晰** - API函数签名更加明确

修复后的API函数现在可以正确处理文件上传，确保升级管理功能的完整性和可靠性。
