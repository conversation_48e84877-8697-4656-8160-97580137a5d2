<template>
  <el-dialog
    title="设备升级"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="升级包文件" prop="upgradeFile">
        <el-upload
          ref="upload"
          :action="uploadAction"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :auto-upload="true"
          accept=".bin,.img,.tar,.gz"
        >
          <el-button>
            <i class="el-icon-upload"></i>
            选择升级包
          </el-button>
          <div slot="tip" class="el-upload__tip">
            支持 .bin/.img/.tar/.gz 格式的升级包文件，文件大小不超过 500MB
          </div>
        </el-upload>
      </el-form-item>

      <el-form-item label="升级包信息" v-if="upgradePackageInfo.name">
        <div class="package-info">
          <div class="info-row">
            <span class="label">文件名：</span>
            <span class="value">{{ upgradePackageInfo.name }}</span>
          </div>
          <div class="info-row">
            <span class="label">文件大小：</span>
            <span class="value">{{ upgradePackageInfo.size }}</span>
          </div>
          <div class="info-row">
            <span class="label">版本号：</span>
            <span class="value">{{ upgradePackageInfo.version || '未知' }}</span>
          </div>
          <div class="info-row">
            <span class="label">适用设备：</span>
            <span class="value">{{ upgradePackageInfo.deviceType || '通用' }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="选择设备" prop="deviceIds">
        <el-tree
          ref="deviceTree"
          :data="deviceData"
          show-checkbox
          node-key="srcId"
          :props="treeProps"
          :check-strictly="false"
          @check="handleTreeCheck"
          :default-checked-keys="selectedDeviceIds"
          style="max-height: 200px; overflow: auto;"
        />
      </el-form-item>

      <el-form-item label="升级选项">
        <el-checkbox-group v-model="formData.upgradeOptions">
          <el-checkbox value="backup">升级前自动备份</el-checkbox>
          <el-checkbox value="reboot">升级后自动重启</el-checkbox>
          <el-checkbox value="verify">升级后验证</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-alert
        title="升级风险提示"
        type="warning"
        description="设备升级存在风险，升级过程中设备将暂时不可用。建议在业务低峰期进行升级操作，并确保升级包与设备型号匹配。"
        show-icon
        :closable="false"
      />
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="danger" @click="handleSubmit" :loading="submitLoading">开始升级</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { startUpgrade, uploadUpgradePackage, getDeviceList } from '@/api/firewall/upgradeManagement'

export default {
  name: 'UpgradeModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      formData: {
        upgradeFile: '',
        deviceIds: [],
        upgradeOptions: ['backup', 'verify'],
      },
      rules: {
        upgradeFile: [
          { required: true, message: '请选择升级包文件', trigger: 'change' },
        ],
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      fileList: [],
      upgradePackageInfo: {},
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
      uploadAction: '/dev/upgrade/upload',
      uploadHeaders: {
        authorization: 'authorization-text',
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadDeviceData()
      }
    },
  },
  methods: {
    initForm() {
      this.formData = {
        upgradeFile: '',
        deviceIds: [],
        upgradeOptions: ['backup', 'verify'],
      }
      this.selectedDeviceIds = []
      this.fileList = []
      this.upgradePackageInfo = {}
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      // 提取设备ID（type为'1'的节点）
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.type === '1') {
          deviceIds.push(node.srcId)
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    beforeUpload(file) {
      const isValidType = /\.(bin|img|tar|gz)$/i.test(file.name)
      if (!isValidType) {
        this.$message.error('只能上传 .bin/.img/.tar/.gz 格式的升级包文件!')
        return false
      }
      const isLt500M = file.size / 1024 / 1024 < 500
      if (!isLt500M) {
        this.$message.error('升级包文件大小不能超过 500MB!')
        return false
      }
      return true
    },
    handleUploadSuccess(response, file) {
      this.formData.upgradeFile = file.name
      this.upgradePackageInfo = {
        name: file.name,
        size: this.formatFileSize(file.size),
        version: response.version || '未知',
        deviceType: response.deviceType || '通用',
      }
      this.$message.success(`${file.name} 上传成功`)
      // 手动触发表单验证
      this.$refs.form.validateField('upgradeFile')
    },
    handleUploadError(error, file) {
      this.$message.error(`${file.name} 上传失败`)
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$confirm(
            '确认开始升级吗？升级过程中设备将暂时不可用，请确保在业务低峰期进行操作。',
            '升级确认',
            {
              confirmButtonText: '开始升级',
              cancelButtonText: '取消',
              type: 'warning',
            }
          ).then(async () => {
            this.submitLoading = true
            try {
              const params = {
                upgradeFile: this.formData.upgradeFile,
                deviceIds: this.formData.deviceIds.join(','),
                upgradeOptions: this.formData.upgradeOptions.join(','),
              }
              
              const res = await startUpgrade(params)
              if (res.retcode === 0) {
                this.$message.success('升级任务已启动，请在升级管理页面查看进度')
                this.$emit('on-submit')
                this.handleClose()
              } else {
                this.$message.error(res.msg)
              }
            } catch (error) {
              this.$message.error('启动升级失败')
            } finally {
              this.submitLoading = false
            }
          }).catch(() => {})
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.package-info {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  .info-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      color: #666;
      font-weight: 500;
    }

    .value {
      color: #333;
    }
  }
}

:deep(.el-upload__tip) {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

:deep(.el-alert) {
  margin-top: 16px;
}
</style>
