import React, { useState, useEffect, createRef } from "react";
import { message, Modal, Button, Form, Row, Col, Input, Select, DatePicker } from "antd";
import moment from "moment";
import SbrTable from "@/components/SbrTable";

import View from './components/view';
import {tacticsPages, tacticsDeleteR } from "./services";
import styles from "./index.less";
const tableRef = createRef();
const viewRef = createRef();
const { RangePicker } = DatePicker;
const List = (props) => {
  const { getFieldDecorator, resetFields, validateFields } = props.form;

  // 列表数据
  const [tableList, setTableList] = useState([]);
  // 查询条件
  const [queryValue, setQueryValue] = useState({});
  const [loading, setLoading] = useState(false);
  // 编辑数据
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  useEffect(
    () => {
      getSourceData(true);
    },
    [queryValue]
  );

  // 查询列表
  const getSourceData = async (isSearch=false) => {
    try {
      setLoading(true);
      const res = await tacticsPages(isSearch?{
        pageIndex: 1,
        pageSize: 10,
        ...queryValue,
      }:{
        ...tableRef?.current?.getValue(),
        ...queryValue,
      });
      if (res.retcode == 0) {
        setTableList(res.data);
        setLoading(false);
        setSelectedRowKeys([])
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  };
  // 删除
  const deleteProtocol = (record={}) => {
      Modal.confirm({
        title: "删除",
        content: "确定要删除选中协议记录吗?删除后不可恢复",
        centered: true,
        okText: "确认",
        cancelText: "取消",
        onOk: async () => {
          try {
            const res = await tacticsDeleteR({ ids: record.id });
            if (res.retcode == 0) {
              message.success("删除成功");
              tableRef.current.calcPageNo(tableList, 1)
              getSourceData();
            } else {
              message.error(res.msg);
            }
          } catch (err) {
            console.log(err);
          }
        },
      });
  };
  // 批量删除
  const batchDeleteProtocol = () => {
    if (selectedRowKeys.length) {
      Modal.confirm({
        title: "删除",
        content: "确定要删除选中策略记录吗?删除后不可恢复",
        centered: true,
        okText: "确认",
        cancelText: "取消",
        onOk: async () => {
          try {
            const res = await tacticsDeleteR({ ids: selectedRowKeys.join(',') });
            if (res.retcode == 0) {
              message.success("删除成功");    
              tableRef.current.calcPageNo(tableList, selectedRowKeys.length)            
              getSourceData();
            } else {
              message.error(res.msg);
            }
          } catch (err) {
            console.log(err);
          }
        },
      });
    } else {
      message.error("至少选中一条数据");
    }
  }

  // 列表数据
  const columns = [
    {
      title: "序号",
      key: "key",
      dataIndex: "key",
      width: 50,
      render: (text, record, index) => {
        return `${index + 1}`;
      },
    },
    {
      title: "名称",
      key: "protocolName",
      dataIndex: "protocolName",
    },
    {
      title: "时间",
      key: "addTime",
      dataIndex: "addTime",
      render: (text, record) => (text === '-' ? text : moment(text).format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      title: "状态",
      key: "status",
      dataIndex: "status",
      render: (text, record) => (
        <>
        <span
          className={
            record.status == '0'
              ? 'redColor'
              : 'blueColor'                                    
          }
        >          
          {(record.status == "0" &&  record.operateType == "0" ) ? "下发失败" :( record.status == "1" &&  record.operateType == "0" )?"下发成功":
          (record.status == "0" &&  record.operateType == "1" ) ? "同步失败":( record.status == "1" &&  record.operateType == "1" )?"同步成功":''
          }
        </span>
      </>
      )
    },
    {
      title: "操作类型",
      key: "operateType",
      dataIndex: "operateType",
      render: (text, record) => {
        return record.operateType == "0"
          ? "下发" : "同步";
      },
    },
    {
      title: "操作数量",
      key: "counts",
      dataIndex: "counts",
    },
    {
      title: "描述",
      key: "description",
      dataIndex: "description",
      width:500,
      ellipsis: true,
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      render: (text, record) => (
        <>
          <div className="table-option">
            <a
              onClick={() => {
                deleteProtocol(record);
              }}
            >
              删除
            </a>
            {/* <a
              onClick={() => {
                handleAdd(record);
              }}
            >
              查看详细
            </a> */}
          </div>
        </>
      ),
    },
  ];
 // 查看
 const view = (record = {}) => {
    viewRef.current.showDrawer(record);
  };



  // 条件查询
  const handleSearch = (e) => {
    e.preventDefault();
   validateFields((err, values) => {
        if (!err) {
          if (values['recordTime'] && values['recordTime'].length > 0) {
            values.startTime = moment(values['recordTime'][0]).format(
              'YYYY-MM-DD',
            );
            values.endTime =
              `${moment(values['recordTime'][1]).format('YYYY-MM-DD')} ` + `23:59:59`;
            delete values['recordTime'];
          } else {
            values.startTime = null;
            values.endTime = null;
          }
            setQueryValue(values);
        }
      });
  };

  // 条件清除
  const handleReset = () => {
    resetFields();
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };
  return (
    <div>
      <Form
        className="searchBg"
        onSubmit={handleSearch}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <Row>
          <Col span={6}>
            <Form.Item label="记录日期">
              {getFieldDecorator("recordTime", {})(
                <RangePicker />
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
          <Form.Item label="记录类型">
              {getFieldDecorator("type", {
                initialValue: "",
              })(
                <Select>
                  <Option value=''>全部</Option>
                  <Option value={0}>下发记录</Option>
                  <Option value={1}>同步记录</Option>                
                </Select>
              )}
            </Form.Item>
          </Col>        
          <Col span={8} className="searchBtn">
            <Button type="primary" htmlType="submit">
              查询
            </Button>
            <Button style={{ marginLeft: 15 }} onClick={handleReset}>
              清除
            </Button>
          </Col>
        </Row>
      </Form>
      <div style={{ marginBottom: 20, marginTop: 20 }}>
        <Button style={{ borderRadius: 2 }} onClick={()=>{batchDeleteProtocol()}}>
          批量删除
        </Button>
      </div>
      <div className={`tableBg`}>
        <SbrTable
          columns={columns}
          scroll={false}
          tableList={tableList}
          getSourceData={getSourceData}
          style={{ wordWrap: "break-word", wordBreak: "break-all" }}
          rowKey={(record) => record.id}
          ref={tableRef}
          loading={loading}
          rowSelection={rowSelection}
        />
      </div>
      <View ref={viewRef} />
    </div>
  );
};

export default Form.create()(List);
