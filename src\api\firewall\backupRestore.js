import request from '@/util/request'

/**
 * 备份还原-查询接口
 * @param params
 * @returns {Promise}
 */
export function getBackupList(params) {
  return request({
    url: '/dev/backup/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 备份还原-创建备份
 * @param data
 * @returns {Promise}
 */
export function createBackup(data) {
  return request({
    url: '/dev/backup/create',
    method: 'post',
    data: data || {},
  })
}

/**
 * 备份还原-还原备份
 * @param data
 * @returns {Promise}
 */
export function restoreBackup(data) {
  return request({
    url: '/dev/backup/restore',
    method: 'post',
    data: data || {},
  })
}

/**
 * 备份还原-删除备份
 * @param data
 * @returns {Promise}
 */
export function deleteBackup(data) {
  return request({
    url: '/dev/backup/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 备份还原-下载备份
 * @param data
 * @returns {Promise}
 */
export function downloadBackup(data) {
  return request({
    url: '/dev/backup/download',
    method: 'post',
    data: data || {},
    responseType: 'blob',
  })
}
