<template>
  <el-dialog title="选择设备" :visible.sync="dialogVisible" width="800px" :before-close="handleClose">
    <div class="device-selector">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="设备名称:">
          <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备IP:">
          <el-input v-model="searchForm.deviceIp" placeholder="请输入设备IP" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 设备列表 -->
      <div class="device-list">
        <el-table :data="deviceList" v-loading="loading" height="400" @selection-change="handleSelectionChange" row-key="id">
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称"></el-table-column>
          <el-table-column prop="ip" label="设备IP"></el-table-column>
          <el-table-column prop="groupName" label="设备分组"></el-table-column>
          <el-table-column prop="status" label="在线状态">
            <template slot-scope="scope">
              <span :class="scope.row.status === 1 ? 'status-online' : 'status-offline'">
                {{ scope.row.status === 1 ? '在线' : '离线' }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        ></el-pagination>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定 (已选{{ selectedDevicesTemp.length }}台)</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getSentineDeviceList } from '@/api/sentine/deviceManagement'

export default {
  name: 'DeviceSelectorModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectedDevices: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      searchForm: {
        deviceName: '',
        deviceIp: '',
      },
      deviceList: [],
      selectedDevicesTemp: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.selectedDevicesTemp = [...this.selectedDevices]
        this.getDeviceList()
      }
    },
  },
  methods: {
    async getDeviceList() {
      this.loading = true
      const payload = {
        _limit: this.pagination.pageSize,
        _page: this.pagination.currentPage,
        queryParams: {
          fireName: this.searchForm.deviceName,
          originIp: this.searchForm.deviceIp,
        },
        type: 4, // 哨兵设备类型
      }

      try {
        const res = await getSentineDeviceList(payload)
        if (res.retcode === 0) {
          this.deviceList = res.data.items || []
          this.pagination.total = res.data.total || 0

          // 设置已选中的设备
          this.$nextTick(() => {
            this.deviceList.forEach((device) => {
              const isSelected = this.selectedDevicesTemp.some((selected) => selected.id === device.id)
              if (isSelected) {
                this.$refs.deviceTable && this.$refs.deviceTable.toggleRowSelection(device, true)
              }
            })
          })
        } else {
          this.$message.error(res.message || '获取设备列表失败')
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.pagination.currentPage = 1
      this.getDeviceList()
    },

    handleResetSearch() {
      this.searchForm = {
        deviceName: '',
        deviceIp: '',
      }
      this.handleSearch()
    },

    handleSelectionChange(selection) {
      this.selectedDevicesTemp = selection
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getDeviceList()
    },

    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getDeviceList()
    },

    handleClose() {
      this.$emit('update:visible', false)
    },

    handleConfirm() {
      this.$emit('on-confirm', this.selectedDevicesTemp)
    },
  },
}
</script>

<style lang="scss" scoped>
.device-selector {
  .search-form {
    margin-bottom: 20px;
  }

  .device-list {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    text-align: right;
  }
}

.status-online {
  color: #67c23a;
}

.status-offline {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
