<template>
  <el-dialog
    title="升级进度"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="progress-content" v-loading="loading">
      <div class="task-info">
        <h3>{{ currentTask.taskName }}</h3>
        <p>升级包：{{ currentTask.upgradeFile }}</p>
        <p>设备数量：{{ currentTask.deviceCount }}</p>
      </div>

      <div class="overall-progress">
        <h4>总体进度</h4>
        <el-progress 
          :percentage="overallProgress" 
          :status="getProgressStatus(currentTask.status)"
          :stroke-width="20"
        ></el-progress>
      </div>

      <div class="device-progress">
        <h4>设备升级详情</h4>
        <el-table :data="deviceProgressList" size="mini" height="300">
          <el-table-column prop="deviceName" label="设备名称"></el-table-column>
          <el-table-column prop="deviceIp" label="设备IP"></el-table-column>
          <el-table-column prop="progress" label="进度" width="200">
            <template slot-scope="scope">
              <el-progress 
                :percentage="scope.row.progress" 
                :status="getDeviceProgressStatus(scope.row.status)"
                :stroke-width="8"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="getStatusClass(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="消息" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleRefresh" :loading="refreshLoading">刷新</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getUpgradeProgress } from '@/api/firewall/upgradeManagement'

export default {
  name: 'ProgressModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentTask: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      refreshLoading: false,
      overallProgress: 0,
      deviceProgressList: [],
      timer: null,
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadProgress()
        this.startTimer()
      } else {
        this.stopTimer()
      }
    },
  },
  methods: {
    async loadProgress() {
      if (!this.currentTask.id) return
      
      this.loading = true
      try {
        const res = await getUpgradeProgress({ taskId: this.currentTask.id })
        if (res.retcode === 0) {
          this.overallProgress = res.data.overallProgress || 0
          this.deviceProgressList = res.data.deviceProgress || []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取升级进度失败')
      } finally {
        this.loading = false
      }
    },
    async handleRefresh() {
      this.refreshLoading = true
      await this.loadProgress()
      this.refreshLoading = false
    },
    startTimer() {
      // 每5秒自动刷新进度
      this.timer = setInterval(() => {
        this.loadProgress()
      }, 5000)
    },
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    getProgressStatus(status) {
      if (status === 'success') return 'success'
      if (status === 'failed') return 'exception'
      return null
    },
    getDeviceProgressStatus(status) {
      if (status === 'success') return 'success'
      if (status === 'failed') return 'exception'
      return null
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'upgrading': '升级中',
        'success': '成功',
        'failed': '失败',
      }
      return statusMap[status] || '-'
    },
    getStatusClass(status) {
      const classMap = {
        'pending': 'status-info',
        'upgrading': 'status-warning',
        'success': 'status-success',
        'failed': 'status-failed',
      }
      return classMap[status] || ''
    },
    handleClose() {
      this.dialogVisible = false
      this.stopTimer()
    },
  },
  beforeDestroy() {
    this.stopTimer()
  },
}
</script>

<style lang="scss" scoped>
.progress-content {
  .task-info {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 20px;

    h3 {
      margin: 0 0 10px 0;
      color: #333;
    }

    p {
      margin: 5px 0;
      color: #666;
    }
  }

  .overall-progress {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #333;
    }
  }

  .device-progress {
    h4 {
      margin: 0 0 10px 0;
      color: #333;
    }
  }
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.status-info {
  color: #909399;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.status-warning {
  color: #e6a23c;
}
</style>
