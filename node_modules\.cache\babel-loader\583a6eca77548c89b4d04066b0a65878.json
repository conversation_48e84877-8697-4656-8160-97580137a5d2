{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\InspectionManage\\components\\AddInspectionModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\InspectionManage\\components\\AddInspectionModal.vue", "mtime": 1750124469059}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}