{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\ProtocolSet\\components\\AddProtocolModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\ProtocolSet\\components\\AddProtocolModal.vue", "mtime": 1750123433125}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}