{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\TabsChange.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\TabsChange.vue", "mtime": 1749799266715}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}