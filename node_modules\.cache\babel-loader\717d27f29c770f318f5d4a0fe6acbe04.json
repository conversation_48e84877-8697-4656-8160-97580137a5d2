{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\ProtocolSet\\components\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\ProtocolSet\\components\\DeviceComponent.vue", "mtime": 1750123462003}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}