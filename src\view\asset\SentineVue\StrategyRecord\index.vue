<template>
  <div>
    <!-- 搜索表单 -->
    <el-form ref="searchForm" :model="searchForm" class="searchBg" label-width="100px" :inline="true">
      <el-row>
        <el-col :span="6">
          <el-form-item label="策略名称">
            <el-input v-model="searchForm.name" placeholder="策略名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="记录日期">
            <el-date-picker
              v-model="searchForm.recordTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="searchBtn">
          <el-button type="primary" @click="handleSearch">
            查询
          </el-button>
          <el-button style="margin-left: 15px" @click="handleReset">
            清除
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <div style="margin-bottom: 20px; margin-top: 20px">
      <el-button style="border-radius: 2px" @click="batchDeleteProtocol">
        批量删除
      </el-button>
    </div>

    <!-- 表格 -->
    <div class="tableBg">
      <el-table
        v-loading="loading"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        style="word-wrap: break-word; word-break: break-all"
        height="400"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="50" :index="getTableIndex" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="addTime" label="时间">
          <template slot-scope="scope">
            {{ formatTime(scope.row.addTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span :class="scope.row.status == '0' ? 'redColor' : 'blueColor'">
              {{ getStatusText(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="operateType" label="操作类型">
          <template slot-scope="scope">
            {{ scope.row.operateType == '0' ? '下发' : '同步' }}
          </template>
        </el-table-column>
        <el-table-column prop="counts" label="操作数量" />
        <el-table-column prop="description" label="描述" width="500" show-overflow-tooltip />
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <div class="table-option">
              <el-button type="text" size="small" @click="deleteProtocol(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 15px; text-align: right"
      />
    </div>
  </div>
</template>

<script>
import { tacticsPages, tacticsDeleteR } from './services'
import dayjs from 'dayjs'

export default {
  name: 'StrategyRecord',
  data() {
    return {
      // 搜索表单
      searchForm: {
        name: '',
        recordTime: null,
      },
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false,
      // 选中的行
      selectedRowKeys: [],
      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
    }
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
              ...this.getQueryParams(),
            }
          : {
              pageIndex: this.pagination.currentPage,
              pageSize: this.pagination.pageSize,
              ...this.getQueryParams(),
            }

        const res = await tacticsPages(params)
        if (res.retcode == 0) {
          // 根据原始React版本，数据直接在res.data中，但需要检查是否有分页结构
          if (res.data && Array.isArray(res.data)) {
            // 如果data是数组，直接使用
            this.tableData = res.data
            this.pagination.total = res.data.length
          } else if (res.data && res.data.rows) {
            // 如果data有rows属性，使用rows和total
            this.tableData = res.data.rows || []
            this.pagination.total = res.data.total || 0
          } else {
            // 兜底处理
            this.tableData = []
            this.pagination.total = 0
          }
          this.selectedRowKeys = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (err) {
        console.log(err)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 获取查询参数
    getQueryParams() {
      const params = {}
      if (this.searchForm.name) {
        params.name = this.searchForm.name
      }
      if (this.searchForm.recordTime && this.searchForm.recordTime.length > 0) {
        params.beginDate = this.searchForm.recordTime[0] + ' 00:00:00'
        params.endDate = this.searchForm.recordTime[1] + ' 23:59:59'
      }
      return params
    },

    // 删除单条记录
    deleteProtocol(record = {}) {
      this.$confirm('确定要删除选中协议记录吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        try {
          const res = await tacticsDeleteR({ ids: record.id })
          if (res.retcode == 0) {
            this.$message.success('删除成功')
            // 如果当前页只有一条数据且不是第一页，则回到上一页
            if (this.tableData.length === 1 && this.pagination.currentPage > 1) {
              this.pagination.currentPage--
            }
            this.getSourceData()
          } else {
            this.$message.error(res.msg)
          }
        } catch (err) {
          console.log(err)
          this.$message.error('删除失败')
        }
      })
    },

    // 批量删除
    batchDeleteProtocol() {
      if (this.selectedRowKeys.length) {
        this.$confirm('确定要删除选中策略记录吗?删除后不可恢复', '删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          try {
            const res = await tacticsDeleteR({ ids: this.selectedRowKeys.join(',') })
            if (res.retcode == 0) {
              this.$message.success('删除成功')
              // 计算删除后的页码
              const remainingCount = this.tableData.length - this.selectedRowKeys.length
              if (remainingCount === 0 && this.pagination.currentPage > 1) {
                this.pagination.currentPage--
              }
              this.getSourceData()
            } else {
              this.$message.error(res.msg)
            }
          } catch (err) {
            console.log(err)
            this.$message.error('删除失败')
          }
        })
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.getSourceData(true)
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        name: '',
        recordTime: null,
      }
      this.pagination.currentPage = 1
      this.getSourceData(true)
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRowKeys = selection.map((item) => item.id)
    },

    // 分页大小变化
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.currentPage = 1
      this.getSourceData()
    },

    // 当前页变化
    handleCurrentChange(currentPage) {
      this.pagination.currentPage = currentPage
      this.getSourceData()
    },

    // 格式化时间
    formatTime(time) {
      if (time === '-' || !time) {
        return time
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },

    // 获取状态文本
    getStatusText(record) {
      if (record.status == '0' && record.operateType == '0') {
        return '下发失败'
      } else if (record.status == '1' && record.operateType == '0') {
        return '下发成功'
      } else if (record.status == '0' && record.operateType == '1') {
        return '同步失败'
      } else if (record.status == '1' && record.operateType == '1') {
        return '同步成功'
      }
      return ''
    },

    // 获取表格序号
    getTableIndex(index) {
      return (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
    },
  },
}
</script>

<style lang="scss" scoped>
.searchBg {
  background: #f5f5f5;
  padding: 20px;
  margin-bottom: 20px;
}

.searchBtn {
  text-align: right;
}

.tableBg {
  background: #fff;
  padding: 20px;
}

.table-option {
  display: flex;
  gap: 10px;
}

.redColor {
  color: #f56c6c;
}

.blueColor {
  color: #409eff;
}
</style>
