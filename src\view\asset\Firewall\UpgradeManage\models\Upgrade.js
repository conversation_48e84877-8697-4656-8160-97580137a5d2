import {
	versionSearch,
	addVersion,
	deleteVersion,
	virusSearch,
	addVirus,
	deleteVirus
	} from '@/services/Upgrade/upgrade';

export default {
	namespace: 'upgrade',

	state: {

	},

	effects: {
		*versionSearchData({payload,callback},{call,put}){
			try {
				const response = yield call(versionSearch, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*addVersionData({payload,callback},{call,put}){
			try {
				const response = yield call(addVersion, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*deleteVersionData({payload,callback},{call,put}){
			try {
				const response = yield call(deleteVersion, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*virusSearchData({payload,callback},{call,put}){
			try {
				const response = yield call(virusSearch, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*addVirusData({payload,callback},{call,put}){
			try {
				const response = yield call(addVirus, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*deleteVirusData({payload,callback},{call,put}){
			try {
				const response = yield call(deleteVirus, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
	},

	reducers: {
	
	}
}