import { searchBackupList,
	     addMapData,
	     recoverBackUp,
	   } from '@/services/Restore/restore';

export default {
	namespace: 'restore',

	state: {
		fireWallData:{},
		topoData: {},
		getUserList:[],
		getAuthList: ''
	},

	effects: {
		*searchBackupData({payload,callback},{call,put}){
			try {
				const response = yield call(searchBackupList, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*addData({payload,callback},{call,put}){
			try {
				const response = yield call(addMapData, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}			
		},
		*recoverBackUpData({payload,callback},{call,put}){
			try {
				const response = yield call(recoverBackUp, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},

		*clearData({ payload, callback }, { call, put }) {
			yield put({
				type: 'saveData',
				fireWallData:{},
				getUserList:[],
				getAuthList: ''
			})
		},
	},

	reducers: {
		saveData(state, action) {
			return {
				...state, ...action
			}
		}
	}
}