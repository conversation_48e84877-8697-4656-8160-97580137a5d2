<template>
  <el-drawer
    title="查看工控协议集"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
  >
    <div class="drawer-content" v-loading="loading">
      <div class="detail-block">
        <div class="detail-row">
          <span class="detail-label">协议名称：</span>
          <span class="detail-value">{{ record.name || '-' }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">来源设备：</span>
          <span class="detail-value">{{ record.srcDeviceName || '-' }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">来源IP：</span>
          <span class="detail-value">{{ record.srcIp || '-' }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">协议类型：</span>
          <span class="detail-value">{{ record.protocolType || '-' }}</span>
        </div>
        
        <div class="detail-row" v-if="record.functionCode">
          <span class="detail-label">功能码：</span>
          <span class="detail-value">{{ record.functionCode || '-' }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">端口范围：</span>
          <span class="detail-value">{{ getPortRange(record) }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">备注：</span>
          <span class="detail-value">{{ record.remark || '-' }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">创建时间：</span>
          <span class="detail-value">{{ formatTime(record.createTime) }}</span>
        </div>
        
        <div class="detail-row">
          <span class="detail-label">更新时间：</span>
          <span class="detail-value">{{ formatTime(record.updateTime) }}</span>
        </div>
      </div>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getProtocolSetInfo } from '@/api/firewall/protocolSet'
import dayjs from 'dayjs'

export default {
  name: 'ViewProtocolModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      record: {},
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadData()
      }
    },
  },
  methods: {
    async loadData() {
      if (this.currentData && this.currentData.id) {
        this.loading = true
        try {
          const res = await getProtocolSetInfo({ id: this.currentData.id })
          if (res.retcode === 0) {
            this.record = res.data
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('获取协议集信息失败')
        } finally {
          this.loading = false
        }
      }
    },
    getPortRange(record) {
      if (record.startPort && record.endPort) {
        return `${record.startPort}-${record.endPort}`
      }
      return '-'
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleClose() {
      this.drawerVisible = false
      this.record = {}
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-block {
  flex: 1;
}

.detail-row {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.detail-label {
  width: 120px;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}
</style>
