import React, { Component, Fragment } from "react";
import { Modal, Form, Tabs, TreeSelect, Select, message,Spin  } from "antd";
import { connect } from "dva";
const FormItem = Form.Item;
const Option = Select.Option;
const { TabPane } = Tabs;
const { TreeNode } = TreeSelect;

@Form.create()
@connect(({}) => ({

}))
export default class AddDeviceModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: this.props.visiableRestore,
      inspectionType:'0',
      deviceData:[],  //所有分组和设备集合
      deviceArr:'' ,  //被选中的数量id
      modalLoading:false 
    };
  }
  componentDidMount (){
    this.getGroupList()
  }

   /**
   * 设备
   * @param
   */
    getGroupList =()=> {
      const { dispatch } = this.props;
      dispatch({
        type: "inspectionManagement/addInspectionData",
        payload: {},
        callback:(res)=> {
          if (res.retcode == 0) {
            this.setState({
              deviceData:res.data
            })    
          } else {
            message.error(res.msg);
          }
        },
      });
    };
  handleCancel = () => {
    this.props.handleAddCancel();
  };

  //  设备选中
  handleTreeValue = (value, label, extra) =>{
    let deviceArr 
    if(value.substring(0,1)=='1'){
      deviceArr = value.split(',')[2]
    }
   this.setState({
    deviceArr
   })

  }
  // 保存
  handleSave =()=>{
    const { dispatch, form } = this.props;
    const { deviceArr, inspectionType } = this.state;   
    form.validateFields((err, values) => {
      if (!err) {
        this.setState({
          modalLoading:true
        })
        dispatch({
          type: "restore/addData",
          payload:{ deviceId:deviceArr},
          callback:(res)=> {
            if (res.retcode == 0) {
              message.success("添加成功!");
              this.props.handleAddCancel();
              this.props.getDeviceList();
            } else {
              message.error(res.msg);
            }
            this.setState({
              modalLoading:false
            })
          },
        });
      }
    });
  };
  renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.childList) {
        return (
          <TreeNode title={item.name} key={item.compId} disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId}>
            {this.renderTreeNodes(item.childList)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.name} key={item.compId} disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId} />;
    });
  };

  render() {
    const { visible,deviceData,modalLoading } = this.state;
    const {
      form: { getFieldDecorator },
      currentConfig: currentPageInfo,
      // saveOnClick,
      // visiableInspection,
    } = this.props;  
    return (
      <Fragment>
        <Modal
          visible={visible}
          title="新建备份"
          okText="保存"
          destroyOnClose
          centered
          confirmLoading={modalLoading}
          onCancel={this.handleCancel}
          onOk={this.handleSave}
          width={650}
          maskClosable={false}
        >         
          <Form >
            <FormItem
              labelCol={{ span: 3}}
              wrapperCol={{ span: 16 }}
              label="选择设备："
            >
              {getFieldDecorator("deviceIds", {
                   rules: [
                    {
                      required: true,
                      message: "请选择设备",
                    },
                  ],
                // initialValue:''
              })( <TreeSelect
                // treeCheckable
                onChange={this.handleTreeValue}
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择设备"
              >
                {this.renderTreeNodes(deviceData)}
              </TreeSelect>)}
            </FormItem>     
          </Form>
        
        </Modal>
      </Fragment>
    );
  }
}
