import { searchInspectionList,
	     addInspectionAll,
	     addInspectionSave,
	     deleteInspection,
	     downloadExcel,
	     } from '@/services/InspectionManagement/InspectionManagement';
import { getTopoMapData, setTopoMapData } from "@/services/NetTopoMap/netTopoMap";

export default {
	namespace: 'inspectionManagement',

	state: {
		fireWallData:{},
		topoData: {},
		getUserList:[],
		getAuthList: ''
	},

	effects: {
		*searchData({payload,callback},{call,put}){
			try {
				const response = yield call(searchInspectionList, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*addInspectionData({payload,callback},{call,put}){
			try {
				const response = yield call(addInspectionAll, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*addInspectionSaveData({payload,callback},{call,put}){
			try {
				const response = yield call(addInspectionSave, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*deleteInspectionData({payload,callback},{call,put}){
			try {
				const response = yield call(deleteInspection, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*downloadExcelData({payload,callback},{call,put}){
			try {
				const response = yield call(downloadExcel, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*clearData({ payload, callback }, { call, put }) {
			yield put({
				type: 'saveData',
				fireWallData:{},
				getUserList:[],
				getAuthList: ''
			})
		},
	},

	reducers: {
		saveData(state, action) {
			return {
				...state, ...action
			}
		}
	}
}