<template>
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>

        <el-form-item label="地址类型" prop="type">
          <el-radio-group v-model="formData.type" @change="handleTypeChange">
            <el-radio :label="0">IP地址</el-radio>
            <el-radio :label="1">地址范围</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-row v-if="formData.type === 0" :gutter="20">
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ipaddr">
              <el-input v-model="formData.ipaddr" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="子网掩码" prop="mask">
              <el-input v-model="formData.mask" placeholder="请输入子网掩码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="formData.type === 1" :gutter="20">
          <el-col :span="12">
            <el-form-item label="起始IP" prop="ipaddr">
              <el-input v-model="formData.ipaddr" placeholder="请输入起始IP" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束IP" prop="endIp">
              <el-input v-model="formData.endIp" placeholder="请输入结束IP" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
            :rows="3"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addAddressSet, updateAddressSet, getAddressSetInfo } from '@/api/firewall/addressSet'

export default {
  name: 'AddAddressModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      title: '新增地址集',
      formData: {
        id: null,
        name: '',
        type: 0,
        ipaddr: '',
        mask: '',
        endIp: '',
        remark: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5\w]{1,20}$/, message: '字符串长度范围: 1 - 20', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '请选择地址类型', trigger: 'change' },
        ],
        ipaddr: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { pattern: /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/, message: 'IP地址格式不正确', trigger: 'blur' },
        ],
        mask: [
          { required: true, message: '请输入子网掩码', trigger: 'blur', validator: this.validateMaskRequired },
        ],
        endIp: [
          { required: true, message: '请输入结束IP', trigger: 'blur', validator: this.validateEndIpRequired },
          { pattern: /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/, message: 'IP地址格式不正确', trigger: 'blur' },
        ],
        remark: [
          { max: 30, message: '备注长度不超过30字', trigger: 'blur' },
          { validator: this.validateRemark, trigger: 'blur' },
        ],
      },
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
  },
  methods: {
    async initForm() {
      if (this.currentData && this.currentData.id) {
        this.title = '编辑地址集'
        this.loading = true
        try {
          const res = await getAddressSetInfo({ id: this.currentData.id })
          if (res.retcode === 0) {
            this.formData = {
              id: res.data.id,
              name: res.data.name,
              type: res.data.type,
              ipaddr: res.data.ipaddr,
              mask: res.data.mask,
              endIp: res.data.endIp,
              remark: res.data.remark,
            }
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('获取地址集信息失败')
        } finally {
          this.loading = false
        }
      } else {
        this.title = '新增地址集'
        this.formData = {
          id: null,
          name: '',
          type: 0,
          ipaddr: '',
          mask: '',
          endIp: '',
          remark: '',
        }
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    handleTypeChange(value) {
      // 切换地址类型时清空相关字段
      if (value === 0) {
        this.formData.endIp = ''
      } else {
        this.formData.mask = ''
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    validateMaskRequired(rule, value, callback) {
      if (this.formData.type === 0 && !value) {
        callback(new Error('请输入子网掩码'))
      } else {
        callback()
      }
    },
    validateEndIpRequired(rule, value, callback) {
      if (this.formData.type === 1 && !value) {
        callback(new Error('请输入结束IP'))
      } else {
        callback()
      }
    },
    validateRemark(rule, value, callback) {
      if (!value) {
        callback()
        return
      }
      const reg = /^[\u4E00-\u9FA5a-zA-Z0-9\-\_\，\。\,\.\ ]+$/
      if (reg.test(value)) {
        callback()
      } else {
        callback(new Error('只能输入字母、数字、减号、中文、下划线、空格、逗号、句号。'))
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            let res
            if (this.title.includes('新增')) {
              res = await addAddressSet(this.formData)
            } else {
              res = await updateAddressSet(this.formData)
            }

            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    handleClose() {
      this.drawerVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin: 0 10px;
}
</style>
