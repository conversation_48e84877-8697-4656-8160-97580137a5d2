{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\ProgressModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\ProgressModal.vue", "mtime": 1750124694711}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}