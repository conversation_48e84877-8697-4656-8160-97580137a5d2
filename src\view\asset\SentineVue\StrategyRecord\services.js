import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";
//哨兵管理-策略-记录-查询
export async function tacticsPages(params) {
  return request(`/home_dev/sentinel_tactics/records`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//哨兵管理-策略-记录-删除接口
export async function tacticsDeleteR(params) {
  return request(`/home_dev/sentinel_tactics/delete_record`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}









