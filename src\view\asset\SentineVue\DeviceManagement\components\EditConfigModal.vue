<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="600px" :before-close="handleClose" @closed="onDialogClosed">
    <el-form ref="form" :model="formData" label-width="120px" :rules="rules">
      <el-form-item label="设备名称" prop="notes">
        <el-input v-model="formData.notes" placeholder="请输入设备名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="设备重要程度" prop="importance">
        <el-select v-model="formData.importance" placeholder="请选择设备重要程度">
          <el-option label="高" :value="1"></el-option>
          <el-option label="中" :value="2"></el-option>
          <el-option label="低" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备分组" prop="group_id">
        <ElTreeSelect
          v-model="formData.group_id"
          :options="groupData"
          placeholder="请选择分组"
          :props="{ label: 'groupName', children: 'childList', value: 'id' }"
          :clearable="true"
          :accordion="false"
        ></ElTreeSelect>
      </el-form-item>
      <el-form-item label="设备位置" prop="position">
        <el-input v-model="formData.position" placeholder="请输入设备位置"></el-input>
      </el-form-item>
      <el-form-item label="安全责任人" prop="person_liable">
        <el-input v-model="formData.person_liable" placeholder="请输入安全责任人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contact">
        <el-input v-model="formData.contact" placeholder="请输入联系电话"></el-input>
      </el-form-item>
      <el-form-item label="管理IP" prop="ip">
        <el-input v-model="formData.ip" placeholder="管理IP" disabled></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import ElTreeSelect from '@comp/SelectTree/SelectTree'
import { editSentineDevice } from '@/api/sentine/deviceManagement'

export default {
  name: 'EditConfigModal',
  components: {
    ElTreeSelect,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '设备配置',
    },
    groupData: {
      type: Array,
      default: () => [],
    },
    currentConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      formData: {
        notes: '',
        ip: '',
        importance: '',
        group_id: '',
        position: '',
        person_liable: '',
        contact: '',
      },
      rules: {
        notes: [
          { required: true, message: '请输入设备名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/, message: '请输入汉字、字母、数字、短横线或下划线', trigger: 'blur' },
        ],
        contact: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initFormData()
      }
    },
  },
  methods: {
    initFormData() {
      if (this.currentConfig) {
        this.formData = {
          notes: this.currentConfig.notes || '',
          ip: this.currentConfig.ip || '',
          importance: this.currentConfig.importance || '',
          group_id: this.currentConfig.group_id || '',
          position: this.currentConfig.position || '',
          person_liable: this.currentConfig.person_liable || '',
          contact: this.currentConfig.contact || '',
        }
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    onDialogClosed() {
      this.$refs.form && this.$refs.form.resetFields()
    },
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 检查是否有修改
          const hasChanges = this.checkForChanges()
          if (!hasChanges) {
            this.$message.error('配置未更改！')
            return
          }

          this.submitLoading = true

          const payload = {
            device_id: this.currentConfig.id,
            importance: parseInt(this.formData.importance),
            position: this.formData.position || '',
            person_liable: this.formData.person_liable || '',
            contact: this.formData.contact || '',
            group_id: this.formData.group_id || '',
          }

          try {
            const res = await editSentineDevice(payload)
            if (res.retcode === 0) {
              this.$message.success('编辑成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.message || '编辑失败')
            }
          } catch (error) {
            this.$message.error('编辑失败')
          } finally {
            this.submitLoading = false
          }
        } else {
          return false
        }
      })
    },
    checkForChanges() {
      const current = this.currentConfig
      const form = this.formData

      return (
        current.importance != form.importance ||
        current.position != form.position ||
        current.person_liable != form.person_liable ||
        current.contact != form.contact ||
        current.group_id != form.group_id
      )
    },
  },
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
