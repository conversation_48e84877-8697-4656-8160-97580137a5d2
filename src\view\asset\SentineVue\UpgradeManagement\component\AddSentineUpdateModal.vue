<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="renderHeader()"
    :destroy-on-close="true"
    width="650px"
    :before-close="handleCancelClick"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item label="选择设备：" prop="deviceIds" :label-width="'120px'" :wrapper-col="{ span: 16 }">
        <el-cascader
          v-model="formData.deviceIds"
          :options="deviceOptions"
          :props="cascaderProps"
          placeholder="请选择设备"
          style="width: 100%"
          @change="handleTreeValue"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="选择授权文件:" prop="file" :label-width="'120px'" :wrapper-col="{ span: 16 }">
        <el-upload ref="upload" :auto-upload="false" :on-change="handleFileChange" :before-upload="beforeUpload" :limit="1" :file-list="fileList">
          <el-button>
            <i class="el-icon-upload"></i>
            导入
          </el-button>
        </el-upload>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancelClick">取消</el-button>
      <el-button type="primary" :loading="modalLoading" @click="handleSave">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { performSoftwareUpgrade, getEquipmentTree } from '@/api/sentine/upgradeManagement'

export default {
  name: 'AddSentineUpdateModal',
  components: {},
  props: {
    visiableAddUpdate: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '0', // "0 版本升级  1 病毒库 2 入侵规则库"
    },
  },
  data() {
    return {
      dialogVisible: false,
      value: 1,
      loading: false,
      showTitle: false,
      deviceData: [], // 所有分组和设备集合
      deviceOptions: [], // 级联选择器选项
      deviceArr: '', // 被选中的设备id
      fileAttList: [],
      file: {},
      modalLoading: false,
      formData: {
        deviceIds: '',
        file: '',
      },
      fileList: [],
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        checkStrictly: true,
      },
      rules: {
        deviceIds: [{ required: true, message: '请选择设备', trigger: 'change' }],
        file: [{ required: true, message: '选择授权文件', trigger: 'change' }],
      },
    }
  },
  watch: {
    visiableAddUpdate(val) {
      this.dialogVisible = val
      if (val) {
        this.getGroupList()
      }
    },
  },
  mounted() {
    this.getGroupList()
  },
  methods: {
    // 获取设备分组列表
    async getGroupList() {
      try {
        // 调用API获取设备分组数据
        const res = await this.getEquipmentTree({ category: 4 })
        if (res.retcode === 0) {
          this.deviceData = res.data
          this.deviceOptions = this.convertToOptions(res.data)
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      }
    },

    // 转换数据为级联选择器格式
    convertToOptions(data) {
      return data.map((item) => {
        const option = {
          value: `${item.type},${item.compId},${item.srcId}`,
          label: item.name,
          disabled: item.type === '0', // 分组不可选
        }

        if (item.childList && item.childList.length > 0) {
          option.children = this.convertToOptions(item.childList)
        }

        return option
      })
    },

    renderTreeNodes(data) {
      return data.map((item) => {
        const nodeData = {
          value: `${item.type},${item.compId},${item.srcId}`,
          label: item.name,
          disabled: item.type === '0',
        }

        if (item.childList) {
          nodeData.children = this.renderTreeNodes(item.childList)
        }

        return nodeData
      })
    },

    renderHeader() {
      return '新建'
    },

    handleCancelClick() {
      this.$emit('handle-add-cancel')
    },

    // 设备选中
    handleTreeValue(value) {
      if (value && value.length > 0) {
        const selectedValue = value[value.length - 1] // 获取最后一级的值
        if (selectedValue && selectedValue.substring(0, 1) === '1') {
          this.deviceArr = selectedValue.split(',')[2]
        }
        this.formData.deviceIds = selectedValue
      }
    },

    // 文件选择
    handleFileChange(file) {
      this.file = file.raw
      this.formData.file = file.raw
    },

    beforeUpload() {
      return false // 阻止自动上传
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.modalLoading = true

          try {
            // 创建FormData对象
            const formData = new FormData()
            formData.append('deviceIds', this.deviceArr)
            formData.append('uploadFile', this.file)
            formData.append('type', this.type)

            const res = await performSoftwareUpgrade(formData)

            if (res.retcode === 0) {
              this.$message.success(res.msg)
              this.handleCancelClick()
              this.$emit('get-update-list')
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.modalLoading = false
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
