import React, { Component, Fragment } from "react";
import {
    <PERSON>,
    Alert,
    Button,
    Table,
    Pagination,
    Row,
    Col,
    Form,
    Select,
    DatePicker,
    Input,
    Modal,
    Tabs,
    message,
    Icon
} from "antd";
import { connect } from "dva";
import AddUpdateModal from "./AddUpdateModal"
import custom from "../../../config/custom.js";

import styles from "../index.less";
import moment from "moment";

const { Column } = Table;
const Option = Select.Option;
const FormItem = Form.Item;
const { primaryColor } = custom;
@Form.create()
@connect(({}) => ({}))
export default class TabsChange extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentPage: 1,
      currentPageSize: 10,
      selectedRowKeys: [],
      searchValue: "",
      totalRecords: 0,
      activeKey: "0",
      visiableAddUpdate: false,
      tableList: [],
      totalSum: 0,
      loading: false,
    };
  }

  componentWillUnmount() {}

  componentDidMount() {
    this.getUpdateList();
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      //   selectedDevList: selectedRows.map(item => item.ip)
    });
  };

  /**
   * 删除
   */
  handleDeleteClick = () => {};

  /**
   * 新建更新
   */
  handleAdd = () => {
    this.setState({
      visiableAddUpdate: true,
    });
  };
  handleAddCancel = () => {
    this.setState({
      visiableAddUpdate: false,
    });
  };
  /**
   * 页面改变事件处理
   * @param pageNumber
   */
  handlePageChange = (pageNumber) => {
    this.setState(
      {
        currentPage: pageNumber,
      },
      () => {
        this.getUpdateList();
      }
    );
  };

  /**
   * 分页大小改变事件处理
   * @param current
   * @param pageSize
   */
  onShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        currentPage: current,
        currentPageSize: pageSize,
      },
      () => {
        this.getUpdateList();
      }
    );
  };

  /**
   * 显示数据总数
   * @param total
   * @returns {string}
   */
  showTotal = (total) => {
    //获取总数状态
    const { totalSum } = this.state;
    return `总数据${totalSum}条`;
  };
  // 更新
  handleUpdate = () => {
    Modal.confirm({
      title: "更新中请稍等",
      // content: `确认删除这${selectedRowKeys.length}条数据吗？`,
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        //   dispatch({
        //     type: "audit/DeleteData",
        //     payload: {
        //       device_ids: selectedRowKeys
        //     },
        //     callback: (res) => {
        //       if (res.code == 0) {
        //         // that.setState({
        //         //   currentPage: 1
        //         // });
        //         that.getDeviceList()
        //         message.success("删除成功");
        //         // that.delDeviceNode(selectedDevList);
        //       } else {
        //         message.error(res.message);
        //       }
        //     }
        //   });
      },
    });
  };

  /**
   * 搜索条件处理
   */
  handleAdvancedSearch = () => {
    const { form } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        let searchValue = {};
        if (values.deviceName) searchValue.deviceName = values.deviceName;
        if (values.updateStatus || values.updateStatus == 0)
          searchValue.updateStatus = values.updateStatus;
        this.setState(
          {
            searchValue: searchValue,
            currentPage: 1,
          },
          () => {
            this.getUpdateList();
          }
        );
      }
    });
  };

  handleReset = () => {
    const { resetFields } = this.props.form;
    resetFields();
  };

  /**
   * 升级列表数据
   */
  getUpdateList = () => {
    this.setState({
      loading: true,
    });
    const { dispatch } = this.props;
    dispatch({
      type: "upgrade/versionSearchData",
      payload: {
        pageSize: this.state.currentPageSize,
        pageIndex: this.state.currentPage,
        updateStatus: this.state.searchValue.updateStatus,
        deviceName: this.state.searchValue.deviceName,
      },
      callback: (res) => {
        //判断是否成功
        if (res.retcode == 0) {
          this.setState({
            tableList: res.data.rows,
            selectedRowKeys: [],
            totalSum: res.data.total,
          });
        } else {
          message.error(res.msg);
        }
        this.setState({
          loading: false,
        });
      },
    });
  };
  // 删除
  handleDelete = (record) => {
    const { dispatch } = this.props;
    Modal.confirm({
      title: "删除",
      content: "确定删除选中的数据吗?删除后不可恢复？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        return new Promise((resolve, reject) => {
          dispatch({
            type: "upgrade/deleteVersionData",
            payload: {
              ids: record.id,
            },
            callback: (res) => {
              if (res.retcode == 0) {
                this.handleVirus();
                message.success("删除成功");
                // that.delDeviceNode(selectedDevList);
              } else {
                message.error(res.msg);
              }
              resolve();
            },
          });
        }).catch((err) => {
          console.log(err);
        });
      },
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    let {
      selectedRowKeys,
      currentPage,
      currentPageSize,
      tableList,
      totalSum,
      notSelectedRowKeys,
      currentSelNum,
      startValue,
      endValue,
      curPageSelNum,
      totalRecords,
      alarmEventList,
      visiableAddUpdate,
      status,
      activeKey,
      loading,
    } = this.state;
    if (alarmEventList) {
      for (var i = 0; i < alarmEventList.length; i++) {
        alarmEventList[i].eventName = alarmEventList[i].log_category_cn;
        alarmEventList[i].eventFeature = alarmEventList[i].log_category_cn;
        alarmEventList[i]["number"] =
          (currentPage - 1) * currentPageSize + i + 1;
      }
    }

    //初始化选择表格属性
    const rowSelection = {
      // selectedRowKeys: typeof curSelectedRowKeys[0] ==='number'?curSelectedRowKeys:curSelectedRowKeys.map(item=>item.id),
      // onChange: this.onSelectChange,
      // onSelectAll: this.onSelectAll,
      // onSelect: this.onSelect,
    };
    return (
      <Fragment>
        <Card style={{ borderRadius: 8 }} bordered={false}>
          <Form
            className="searchBg"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 14 }}
          >
            <Row>
              <Col span={8}>
                <FormItem
                  // labelCol={{ span: 6 }}
                  // wrapperCol={{ span: 14 }}
                  label="设备名称:"
                >
                  {getFieldDecorator("deviceName", {
                    rules: [
                      {
                        pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,
                        message: "请输入汉字、字母、数字、短横线或下划线",
                      },
                    ],
                  })(<Input maxLength={50} placeholder="设备名称" />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="升级类型">
                  {getFieldDecorator("updateStatus", {
                    initialValue: "",
                  })(
                    <Select>
                      <Option value="">全部</Option>
                      <Option value={0}>升级成功</Option>
                      <Option value={1}>升级失败</Option>
                    </Select>
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="searchBtn">
                <Button
                  type="primary"
                  loading={loading}
                  onClick={this.handleAdvancedSearch}
                >
                  查询
                </Button>
                <Button style={{ marginLeft: 15 }} onClick={this.handleReset}>
                  清除
                </Button>
              </Col>
            </Row>
          </Form>
        </Card>
        <div style={{ marginBottom: 20, marginTop: 20 }}>
          {/* <Button type="primary" style={{ marginRight: 8,background:'#108EE9',color:'#FFFFFF', borderRadius: 2 }} onClick={this.openModal}>导出日志</Button> */}
          <Button
            type="primary"
            style={{ marginRight: 8 }}
            onClick={this.handleAdd}
          >
            新建更新
          </Button>
          {/* <Button style={{ borderRadius: 2 }} onClick={this.handleDeleteClick}>
                    批量病毒库更新
                </Button> */}
        </div>
        <Card
          bordered={false}
          style={{ borderRadius: 8 }}
          className="TableContainer"
        >
          <Table
            rowSelection={rowSelection}
            className={styles.tableHeader}
            dataSource={tableList}
            loading={loading}
            rowKey={(record) => record.id}
            pagination={false}
            locale={{ emptyText: "暂无数据信息" }}
            scroll={{ x: 1500 }}
          >
            <Column
              title="序号"
              dataIndex="number"
              key="number"
              width={50}
              render={(text, record, index) => {
                return `${index + 1}`;
              }}
            />
            <Column
              title="设备名称"
              dataIndex="deviceName"
              key="deviceName"
              width={80}
            />
            <Column
              title="设备IP"
              dataIndex="deviceIp"
              key="deviceIp"
              width={80}
            />
            <Column
              title="时间"
              dataIndex="updateTime"
              key="updateTime"
              width={120}
              render={(text, record, index) => {
                return record.updateTime == null
                  ? ""
                  : `${moment(record.updateTime).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )}`;
              }}
            />
            <Column
              title="描述"
              dataIndex="description"
              key="description"
              width={70}
            />
            <Column
              title="版本"
              dataIndex="versionNum"
              key="versionNum"
              width={70}
            />
            <Column
              title="升级状态"
              dataIndex="updateStatus"
              key="updateStatus"
              width={70}
              render={(text, record, index) => {
                return record.updateStatus == 0 ? "成功" : "失败";
              }}
            />
            <Column
              title="操作"
              dataIndex="action"
              key="action"
              align="left"
              width={120}
              render={(text, record) => (
                <div style={{ color: primaryColor }}>
                  <a
                    style={{ borderRadius: 2, color: primaryColor }}
                    onClick={() => {
                      this.handleDelete(record);
                    }}
                  >
                    删除
                  </a>
                </div>
              )}
            />
          </Table>
        </Card>
        <Row type="flex" justify="end">
          <Pagination
            current={currentPage}
            showQuickJumper
            showSizeChanger
            onShowSizeChange={this.onShowSizeChange}
            total={totalSum}
            showTotal={this.showTotal}
            onChange={this.handlePageChange}
          />
        </Row>
        {visiableAddUpdate && (
          <AddUpdateModal
            visiableAddUpdate={visiableAddUpdate}
            handleAddCancel={this.handleAddCancel}
            type="2"
            getUpdateList={this.getUpdateList}
          />
        )}
      </Fragment>
    );
  }
}
