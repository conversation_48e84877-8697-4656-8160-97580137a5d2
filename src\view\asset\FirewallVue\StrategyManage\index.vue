<template>
  <div class="router-wrap-table">
    <div class="page-title">策略管理</div>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="策略管理" name="0">
        <header class="table-header">
          <section class="table-header-main">
            <section class="table-header-search">
              <section v-show="!isShow" class="table-header-search-input">
                <el-input v-model="queryInput.name" clearable placeholder="名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
              </section>
              <section class="table-header-search-button">
                <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="toggleShow">
                  高级搜索
                  <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
                </el-button>
              </section>
            </section>
            <section class="table-header-button">
              <el-button type="primary" @click="handleAdd">新建策略</el-button>
              <el-button type="primary" @click="handleBatchIssue">批量下发</el-button>
              <el-button type="primary" @click="handleSyncProtocol">同步设备策略</el-button>
              <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
            </section>
          </section>
          <section class="table-header-extend">
            <el-collapse-transition>
              <div v-show="isShow">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-input v-model="queryInput.name" clearable placeholder="名称" @change="handleQuery"></el-input>
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="queryInput.status" clearable placeholder="状态" @change="handleQuery">
                      <el-option label="全部" value=""></el-option>
                      <el-option label="启用" value="1"></el-option>
                      <el-option label="禁用" value="0"></el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24" align="right">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                    <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-transition>
          </section>
        </header>

        <main class="table-body">
          <section class="table-body-header">
            <h2 class="table-body-title">策略管理</h2>
          </section>
          <section v-loading="loading" class="table-body-main">
            <el-table
              :data="tableData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center"></el-table-column>
              <el-table-column label="序号" width="80" align="center">
                <template slot-scope="scope">
                  {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button type="text" class="el-button--blue" @click="handleView(scope.row)">
                    {{ scope.row.name }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="srcDeviceName" label="来源设备" show-overflow-tooltip></el-table-column>
              <el-table-column prop="srcIp" label="来源ip" width="100"></el-table-column>
              <el-table-column prop="action" label="动作">
                <template slot-scope="scope">
                  {{ scope.row.action === '1' ? '允许' : '拒绝' }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <span :class="scope.row.status === '1' ? 'status-success' : 'status-failed'">
                    {{ scope.row.status === '1' ? '启用' : '禁用' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注"></el-table-column>
              <el-table-column label="操作" width="250" fixed="right">
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <el-button class="el-button--blue" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleToggle(scope.row)">
                      {{ scope.row.status === '1' ? '禁用' : '启用' }}
                    </el-button>
                    <el-button class="el-button--blue" type="text" @click="handleIssue(scope.row)">策略下发</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleScope(scope.row)">应用范围</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </main>
        <footer class="table-footer">
          <el-pagination
            v-if="pagination.visible"
            small
            background
            align="right"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          ></el-pagination>
        </footer>
      </el-tab-pane>

      <el-tab-pane label="策略记录" name="1">
        <strategy-record />
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑策略对话框 -->
    <add-strategy-modal :visible.sync="addModalVisible" :current-data="currentData" @on-submit="handleAddSubmit"></add-strategy-modal>

    <!-- 查看策略对话框 -->
    <view-strategy-modal :visible.sync="viewModalVisible" :current-data="currentData"></view-strategy-modal>

    <!-- 应用范围对话框 -->
    <scope-modal :visible.sync="scopeModalVisible" :current-data="currentData" @on-submit="handleScopeSubmit"></scope-modal>

    <!-- 设备选择组件 -->
    <device-component ref="deviceComponent" :operation-type="operationType" @on-submit="handleDeviceSubmit"></device-component>
  </div>
</template>

<script>
import { getStrategyList, deleteStrategy, issueStrategy, syncStrategyFromDevice, toggleStrategy, setStrategyScope } from '@/api/firewall/strategyManagement'
import AddStrategyModal from './components/AddStrategyModal.vue'
import ViewStrategyModal from './components/ViewStrategyModal.vue'
import ScopeModal from './components/ScopeModal.vue'
import DeviceComponent from './components/DeviceComponent.vue'
import StrategyRecord from './components/StrategyRecord.vue'

export default {
  name: 'StrategyManage',
  components: {
    AddStrategyModal,
    ViewStrategyModal,
    ScopeModal,
    DeviceComponent,
    StrategyRecord,
  },
  data() {
    return {
      activeTab: '0',
      isShow: false,
      loading: false,
      queryInput: {
        name: '',
        status: '',
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addModalVisible: false,
      viewModalVisible: false,
      scopeModalVisible: false,
      currentData: null,
      operationType: '', // '1': 下发, '2': 同步
    }
  },
  mounted() {
    this.getStrategyList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getStrategyList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getStrategyList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取策略列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.name) params.name = this.queryInput.name
      if (this.queryInput.status !== '') params.status = this.queryInput.status
      return params
    },
    handleTabClick(tab) {
      // 标签页切换逻辑
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getStrategyList()
    },
    handleReset() {
      this.queryInput = {
        name: '',
        status: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.currentData = null
      this.addModalVisible = true
    },
    handleEdit(record) {
      this.currentData = record
      this.addModalVisible = true
    },
    handleView(record) {
      this.currentData = record
      this.viewModalVisible = true
    },
    handleScope(record) {
      this.currentData = record
      this.scopeModalVisible = true
    },
    handleDelete(record) {
      this.$confirm('确定要删除选中策略吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteStrategy({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getStrategyList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中策略吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            const res = await deleteStrategy({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getStrategyList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleToggle(record) {
      const action = record.status === '1' ? '禁用' : '启用'
      this.$confirm(`确定要${action}该策略吗?`, action, {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await toggleStrategy({
              id: record.id,
              status: record.status === '1' ? '0' : '1'
            })
            if (res.retcode === 0) {
              this.$message.success(`${action}成功`)
              this.getStrategyList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error(`${action}失败`)
          }
        })
        .catch(() => {})
    },
    handleIssue(record) {
      this.operationType = '1'
      this.$refs.deviceComponent.showDrawer(record, [record.id])
    },
    handleBatchIssue() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }
      this.operationType = '1'
      const ids = this.selectedRows.map(row => row.id)
      this.$refs.deviceComponent.showDrawer({}, ids)
    },
    handleSyncProtocol() {
      this.operationType = '2'
      this.$refs.deviceComponent.showDrawer({}, [])
    },
    handleAddSubmit() {
      this.addModalVisible = false
      this.getStrategyList()
    },
    handleScopeSubmit() {
      this.scopeModalVisible = false
      this.getStrategyList()
    },
    handleDeviceSubmit() {
      this.getStrategyList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getStrategyList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getStrategyList()
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
