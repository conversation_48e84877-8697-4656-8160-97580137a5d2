{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\DeviceManagement\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\DeviceManagement\\index.vue", "mtime": 1750057702374}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}