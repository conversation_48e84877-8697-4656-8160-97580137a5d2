import React, { Component, Fragment } from "react";
import { Modal, Form, Tabs, TreeSelect, Select, message  } from "antd";
import styles from "./index.less";
import { connect } from "dva";
const FormItem = Form.Item;
const Option = Select.Option;
const { TabPane } = Tabs;
const { TreeNode } = TreeSelect;

@Form.create()
@connect(({}) => ({

}))
export default class BatchInspection extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: this.props.visiableInspection,
      inspectionType:'1',
      deviceData:[],  //所有分组和设备集合
      deviceArr:[],   //被选中的数量id集合
      modalLoading:false, 
    };
  }
  componentDidMount (){
    this.getGroupList()
  }

   /**
   * 设备
   * @param
   */
    getGroupList =()=> {
      const { dispatch } = this.props;
      dispatch({
        type: "inspectionManagement/addInspectionData",
        payload: {},
        callback:(res)=> {
          if (res.retcode == 0) {
            this.setState({
              deviceData:res.data
            })    
          } else {
            message.error(res.msg);
          }
        },
      });
    };
  handleCancel = () => {
    this.props.handleCancelClick();
  };

  //  设备选中
  handleTreeValue = (value, label, extra) =>{
    let deviceArr =value.map((item)=>{
      if(item.substring(0,1)=='1'){
        return item.split(',')[2]
      }
    })
   this.setState({
    deviceArr
   })

  }
  // 保存
  handleSave =()=>{
    const { dispatch, form } = this.props;
    const { deviceArr, inspectionType } = this.state;   
    form.validateFields((err, values) => {
      if (!err) {
        this.setState({
          modalLoading:true
        })
        dispatch({
          type: "inspectionManagement/addInspectionSaveData",
          payload:inspectionType=='2'? {
            deviceIds:deviceArr.join(),
            inspectionType:inspectionType,
            timePeriod:values.timePeriod,
          }:{
            deviceIds:deviceArr.join(),
            inspectionType:inspectionType,
          },
          callback:(res)=> {
            if (res.retcode == 0) {
              message.success("添加成功!");
              this.props.handleCancelClick();
              this.props.getInspectionData();
            } else {
              message.error(res.msg);
            }
            this.setState({
              modalLoading:false
            })
          },
        });
      }
    });
  };
  renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.childList) {
        return (
          <TreeNode title={item.name} key={item.compId}  disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId}>
            {this.renderTreeNodes(item.childList)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.name} key={item.compId}  disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId} />;
    });
  };
  changeType = (value) =>{
    this.setState({
      inspectionType:value
    })

  }
  render() {
    const { visible,deviceData, modalLoading } = this.state;
    const {
      form: { getFieldDecorator },
      currentConfig: currentPageInfo,
      // saveOnClick,
      // visiableInspection,
    } = this.props;  
    return (
      <Fragment>
        <Modal
          visible={visible}
          title="新建巡检"
          okText="开始巡检"
          destroyOnClose
          centered
          confirmLoading={modalLoading}
          onCancel={this.handleCancel}
          onOk={this.handleSave}
          width={650}
          maskClosable={false}
        >
          <Form >
            <FormItem
              labelCol={{ span: 3}}
              wrapperCol={{ span: 16 }}
              label="选择设备："
            >
              {getFieldDecorator("deviceIds", {
                   rules: [
                    {
                      required: true,
                      message: "请选择设备",
                    },
                  ],
                // initialValue:''
              })( <TreeSelect
                treeCheckable
                onChange={this.handleTreeValue}
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择设备"
              >
                {this.renderTreeNodes(deviceData)}
              </TreeSelect>)}
            </FormItem>
            <div className={styles.contentTabs}>
            <Tabs defaultActiveKey="1" type='card'  onChange={this.changeType}>
              <TabPane tab="手动巡检" key="1">
              
              </TabPane>
              <TabPane tab="自动周期巡检" key="2">
              <FormItem
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 16 }}
              label="定时开始时间："
            >
              {getFieldDecorator("timePeriod", {
                  // rules: [
                  //   {
                  //     required: true,
                  //     message: "请选择定时开始时间",
                  //   },
                  // ],
                initialValue:'0'
              })( <Select>
                <Option value="0">日</Option>
                <Option value="1">周</Option>
                <Option value="2">月</Option>
              </Select>)}
            </FormItem>
              </TabPane>              
            </Tabs>    
            </div>   
          </Form>
        </Modal>
      </Fragment>
    );
  }
}
