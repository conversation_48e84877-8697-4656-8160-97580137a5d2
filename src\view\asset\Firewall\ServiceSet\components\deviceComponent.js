import React, {
    useState,
    useEffect,
    forwardRef,
    useImperativeHandle,
  } from "react";
  import {
    message,
    Form,
    Row,
    Modal,
    Spin,
    Button,
    TreeSelect,
    Input,
    Radio,
  } from "antd";
  import SbrDrawer from "@/components/SbrDrawer";
  import { addInspectionData,protocolIssued, syncFromDevice } from "../services";
  const { TreeNode } = TreeSelect;
  let AddDevice = (props) => {
    const { refInstance, dispatch, getSourceData,typeButton } = props;
    const { getFieldDecorator, validateFields } = props.form;
    // 是否展示抽屉
    const [visible, setVisible] = useState(false);
    // 查看数据
    const [record, setRecord] = useState({});
    const [loading, setLoading] = useState(false);
    //所有分组和设备集合
    const [deviceData, setDeviceData] = useState([]);
    //被选中的数量id集合
    const [deviceArr, setdeviceArr] = useState([]);
    // 编辑数据
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    useImperativeHandle(refInstance, () => ({
      showDrawer,
    }));
  
    useEffect(() => {}, []);
  
    // 打开抽屉
    const showDrawer = async (record = {},copy=[]) => {
      setVisible(true);
        try {
          setLoading(true);
          const res = await addInspectionData({ id: record.id });
          if (res.retcode == 0) {
            setDeviceData(res.data);
            setSelectedRowKeys(copy);
          } else {
            message.error(res.msg);
          }
          setLoading(false);
        } catch (err) {
          console.log(err);
        }
    };
  
    //关闭抽屉
    const onDrawerClose = () => {
      setVisible(false);
    };

      // 保存
      const handleSubmit = async (e) => {
        e.preventDefault();
        validateFields(async (err, values) => {
          if (!err) {
            Modal.confirm({
              title:
                typeButton == 1
                  ? "服务下发后不可修改，是否确认下发？"
                  : "同步设备服务后不可修改，是否确认同步设备服务？",
              centered: true,
              okText: "确认",
              cancelText: "取消",
              onOk: async () => {
                let res;
                try {
                  setLoading(true);
                  if (typeButton == 1) {
                    res = await protocolIssued({
                      bandDeviceIds: deviceArr.join(),
                      ids: selectedRowKeys.join(","),
                    });
                  } else if (typeButton == 2) {
                    res = await syncFromDevice({
                      bandDeviceIds: deviceArr.join(),
                      ids: selectedRowKeys.join(","),
                    });
                  }

                  if (res.retcode == 0) {
                    message.success(res.msg);
                    setVisible(false);
                    getSourceData();
                  } else {
                    message.error(res.msg);
                    setVisible(false);
                  }
                  setLoading(false);
                } catch (err) {
                  console.log(err);
                }
              },
            });
          }
        });
      };
    //  设备选中
    const  handleTreeValue = (value, label, extra) =>{
        let deviceArr =[]
        value.map((item)=>{
          if(item.substring(0,1)=='1'){
            deviceArr.push(item.split(',')[2]) 
          }
        })
        setdeviceArr(deviceArr)      
    }
  const  renderTreeNodes = (data) => {
        return data.map((item) => {
          if (item.childList) {
            return (
              <TreeNode title={item.name} key={item.compId} disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId}>
                {renderTreeNodes(item.childList)}
              </TreeNode>
            );
          }
          return <TreeNode title={item.name} key={item.compId} disabled={item.type=='0'} value={item.type+','+item.compId+','+item.srcId} />;
        });
      };
  
    return (
      <SbrDrawer
        title={typeButton==1?"服务下发":"同步设备服务"}
        width={800}
        onClose={onDrawerClose}
        visible={visible}
      >
        <Form
          onSubmit={handleSubmit}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <div className="public-block">
            <Spin spinning={loading}>
              <Form.Item name="id" hidden>
                <Input />
              </Form.Item>
              <Form.Item
              labelCol={{ span: 3}}
              wrapperCol={{ span: 16 }}
              label="选择设备："
            >
              {getFieldDecorator("deviceIds", {
                   rules: [
                    {
                      required: true,
                      message: "请选择设备",
                    },
                  ],
                // initialValue:''
              })( <TreeSelect
                treeCheckable
                onChange={handleTreeValue}
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择设备"
              >
                {renderTreeNodes(deviceData)}
              </TreeSelect>)}
            </Form.Item>    
            </Spin>
          </div>
  
          <Row type="flex" justify="center" className="drawer_btns">
            <Button htmlType="submit" type="primary">
              保存
            </Button>
            <Button className="spacing_btn" onClick={onDrawerClose}>
              关闭
            </Button>
          </Row>
        </Form>
      </SbrDrawer>
    );
  };
  AddDevice = Form.create()(AddDevice);
  export default forwardRef((props, ref) => <AddDevice {...props} refInstance={ref} />);
  