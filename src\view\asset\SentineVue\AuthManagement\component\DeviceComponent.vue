<template>
  <el-drawer
    title="设备同步"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="设备名称:">
          <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备IP:">
          <el-input v-model="searchForm.deviceIp" placeholder="请输入设备IP" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="device-list">
        <el-table
          :data="deviceList"
          v-loading="loading"
          height="400"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称"></el-table-column>
          <el-table-column prop="ip" label="设备IP"></el-table-column>
          <el-table-column prop="status" label="在线状态">
            <template slot-scope="scope">
              <span :class="scope.row.status === 1 ? 'status-online' : 'status-offline'">
                {{ scope.row.status === 1 ? '在线' : '离线' }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="syncLoading" @click="handleSync">同步</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getSentineDeviceList } from '@/api/sentine/deviceManagement'
import { syncDeviceAuth } from '@/api/sentine/authManagement'

export default {
  name: 'DeviceComponent',
  data() {
    return {
      drawerVisible: false,
      loading: false,
      syncLoading: false,
      searchForm: {
        deviceName: '',
        deviceIp: '',
      },
      deviceList: [],
      selectedDevices: [],
    }
  },
  methods: {
    showDrawer() {
      this.drawerVisible = true
      this.getDeviceList()
    },
    handleClose() {
      this.drawerVisible = false
      this.resetData()
    },
    resetData() {
      this.searchForm = {
        deviceName: '',
        deviceIp: '',
      }
      this.deviceList = []
      this.selectedDevices = []
    },
    async getDeviceList() {
      this.loading = true
      const payload = {
        fireName: this.searchForm.deviceName,
        originIp: this.searchForm.deviceIp,
        type: 4, // 哨兵设备类型
        _limit: 100,
        _page: 1,
      }

      try {
        const res = await getSentineDeviceList(payload)
        if (res.retcode === 0) {
          this.deviceList = res.data.items || []
        } else {
          this.$message.error(res.message || '获取设备列表失败')
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.getDeviceList()
    },
    handleResetSearch() {
      this.searchForm = {
        deviceName: '',
        deviceIp: '',
      }
      this.getDeviceList()
    },
    handleSelectionChange(selection) {
      this.selectedDevices = selection
    },
    async handleSync() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning('请选择要同步的设备')
        return
      }

      this.syncLoading = true
      const deviceIds = this.selectedDevices.map(device => device.id)

      try {
        const res = await syncDeviceAuth({ deviceIds })
        if (res.retcode === 0) {
          this.$message.success('设备同步成功')
          this.handleClose()
          this.$emit('on-sync-success')
        } else {
          this.$message.error(res.msg || '设备同步失败')
        }
      } catch (error) {
        this.$message.error('设备同步失败')
      } finally {
        this.syncLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-form {
  margin-bottom: 20px;
}

.device-list {
  flex: 1;
  overflow: hidden;
}

.drawer-footer {
  margin-top: 20px;
  text-align: right;
  border-top: 1px solid #e8e8e8;
  padding-top: 20px;
}

.status-online {
  color: #67c23a;
}

.status-offline {
  color: #f56c6c;
}
</style>
