<template>
  <el-drawer
    title="同步信息"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form ref="form" :model="formData" label-width="100px" :rules="rules">
        <el-form-item label="选择设备" prop="deviceIds">
          <el-select
            v-model="formData.deviceIds"
            multiple
            placeholder="请选择设备"
            style="width: 100%"
            @change="handleDeviceChange"
          >
            <el-option
              v-for="device in flatDeviceList"
              :key="device.compId"
              :label="device.name"
              :value="device.compId"
              :disabled="device.type === '0'"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" :loading="syncLoading" @click="handleSync">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getDeviceTreeData } from '@/api/sentine/deviceManagement'
import { syncDeviceAuth } from '@/api/sentine/authManagement'

export default {
  name: 'DeviceComponent',
  data() {
    return {
      drawerVisible: false,
      loading: false,
      syncLoading: false,
      formData: {
        deviceIds: [],
      },
      deviceTreeData: [],
      flatDeviceList: [],
      selectedDeviceIds: [],
      rules: {
        deviceIds: [{ required: true, message: '请选择设备', trigger: 'change' }],
      },
    }
  },
  methods: {
    showDrawer() {
      this.drawerVisible = true
      this.getDeviceTreeData()
    },
    handleClose() {
      this.drawerVisible = false
      this.resetData()
    },
    resetData() {
      this.formData = {
        deviceIds: [],
      }
      this.deviceTreeData = []
      this.flatDeviceList = []
      this.selectedDeviceIds = []
    },
    async getDeviceTreeData() {
      this.loading = true
      try {
        const res = await getDeviceTreeData({ category: 4 })
        if (res.retcode === 0) {
          this.deviceTreeData = res.data || []
          this.flatDeviceList = this.flattenDeviceTree(this.deviceTreeData)
        } else {
          this.$message.error(res.msg || '获取设备树失败')
        }
      } catch (error) {
        this.$message.error('获取设备树失败')
      } finally {
        this.loading = false
      }
    },
    // 将设备树扁平化为列表
    flattenDeviceTree(treeData) {
      const result = []
      const traverse = (nodes) => {
        nodes.forEach(node => {
          result.push({
            compId: node.compId,
            name: node.name,
            type: node.type,
            srcId: node.srcId
          })
          if (node.childList && node.childList.length > 0) {
            traverse(node.childList)
          }
        })
      }
      traverse(treeData)
      return result
    },
    handleDeviceChange(values) {
      // 根据选中的设备ID找到对应的设备信息，只保留type为'1'的设备
      this.selectedDeviceIds = []
      values.forEach(value => {
        const selectedDevice = this.flatDeviceList.find(device => device.compId === value)
        if (selectedDevice && selectedDevice.type === '1') {
          this.selectedDeviceIds.push(selectedDevice.srcId)
        }
      })
    },
    async handleSync() {
      // 验证表单
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.selectedDeviceIds.length === 0) {
            this.$message.warning('请选择要同步的设备')
            return
          }

          this.$confirm('同步信息后不可修改，是否确认同步信息？', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            this.syncLoading = true
            try {
              const res = await syncDeviceAuth({ deviceIds: this.selectedDeviceIds.join(',') })
              if (res.retcode === 0) {
                this.$message.success(res.msg || '同步成功')
                this.handleClose()
                this.$emit('on-sync-success')
              } else {
                this.$message.error(res.msg || '同步失败')
              }
            } catch (error) {
              this.$message.error('同步失败')
            } finally {
              this.syncLoading = false
            }
          }).catch(() => {
            // 用户取消操作
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: 20px;
  text-align: center;
  border-top: 1px solid #e8e8e8;
  padding-top: 20px;
}

.drawer-footer .el-button {
  margin: 0 10px;
}
</style>
