<template>
  <el-drawer
    title="设备同步"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="选择设备" prop="deviceIds">
          <el-tree
            ref="deviceTree"
            :data="deviceData"
            show-checkbox
            node-key="srcId"
            :props="treeProps"
            :check-strictly="false"
            @check="handleTreeCheck"
            :default-checked-keys="selectedDeviceIds"
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">同步</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { syncAuthFromDevice, getDeviceList } from '@/api/firewall/authManagement'

export default {
  name: 'DeviceSyncComponent',
  data() {
    return {
      drawerVisible: false,
      loading: false,
      submitLoading: false,
      formData: {
        deviceIds: [],
      },
      rules: {
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
    }
  },
  methods: {
    async showDrawer() {
      this.drawerVisible = true
      await this.loadDeviceData()
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      // 提取设备ID（type为'1'的节点）
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.type === '1') {
          deviceIds.push(node.srcId)
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$confirm('确认同步选中设备的授权信息吗？', '确认同步', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            this.submitLoading = true
            try {
              const params = {
                deviceIds: this.formData.deviceIds.join(','),
              }
              
              const res = await syncAuthFromDevice(params)
              if (res.retcode === 0) {
                this.$message.success(res.msg || '同步成功')
                this.$emit('on-submit')
                this.handleClose()
              } else {
                this.$message.error(res.msg)
              }
            } catch (error) {
              this.$message.error('同步失败')
            } finally {
              this.submitLoading = false
            }
          }).catch(() => {
            // 用户取消操作
          })
        }
      })
    },
    handleClose() {
      this.drawerVisible = false
      this.formData = {
        deviceIds: [],
      }
      this.selectedDeviceIds = []
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin: 0 10px;
}

:deep(.el-tree) {
  max-height: 400px;
  overflow: auto;
}
</style>
