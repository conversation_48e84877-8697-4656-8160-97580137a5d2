import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  message,
  Form,
  Row,
  Col,
  Spin,
  Button,
  Select,
  Input,
  Radio,
} from "antd";
import SbrDrawer from "@/components/SbrDrawer";
import { ipAddressInfo, ipAddressAdd, ipAddressUpdate } from "../services";
import core from '../../../../utils/validationUnit.js'
const { TextArea } = Input;
let Add = (props) => {
  const { refInstance, getSourceData } = props;
  const { getFieldDecorator, validateFields, setFieldsValue } = props.form;
  // 是否展示抽屉
  const [visible, setVisible] = useState(false);
  //标题
  const [title, setTitle] = useState("");
  // 查看数据
  const [record, setRecord] = useState({});
  const [loading, setLoading] = useState(false);
  // 地址类型
  const [radioStaus, setRadioStaus] = useState(0);
  // 地址范围
  const [mapList, setmapList] = useState([{ firstIp: "", secondIp: "" }]);
  useImperativeHandle(refInstance, () => ({
    showDrawer,
  }));

  useEffect(() => {}, []);

  // 打开抽屉
  const showDrawer = async (record = {}) => {
    setVisible(true);

    if (record.id) {
      setTitle("编辑地址集");
      try {
        setLoading(true);
        const res = await ipAddressInfo({ id: record.id });
        if (res.retcode == 0) {
          setRadioStaus(res.data.type)
          setFieldsValue({
            id: res.data.id,
            category: res.data.category,
            name: res.data.name,
            type: res.data.type,
            ipaddr: res.data.ipaddr,
            mask: res.data.mask,
            endIp: res.data.endIp,
            remark: res.data.remark,
          });
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
      }
    } else {
      setTitle("新增地址集");
    }
  };

  //关闭抽屉
  const onDrawerClose = () => {
    setVisible(false);
  };
  const  validatorRemark  =(rule, value, callback) =>{
    let reg = '/^[\\u4E00-\\u9FA5a-zA-Z0-9\-\_\，\。\,\.\ \]+$/'
    if (core.regex(value, [reg])) {
      callback()
    } else {
      callback(new Error('只能输入字母、数字、减号、中文、下划线、空格、逗号、句号。'))
    }
    }
  // 提交
  const handleSubmit = (e) => {
    e.preventDefault();
    validateFields(async (err, values) => {
      if (!err) {
        let res;
        if (title.indexOf("新增") != -1) {
          res = await ipAddressAdd(values);
        } else {
          res = await ipAddressUpdate(values);
        }
        if (res.retcode == 0) {
          message.success("操作成功");
          getSourceData();
          onDrawerClose();
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      }
    });
  };

  const radioChange = (e) => {
    setRadioStaus(e.target.value);
    if (e.target.value == 0) {
      setFieldsValue({
        ipaddr:'',
        mask:''
      });
    } else {
      setFieldsValue({
        ipaddr:'',
        endIp:''
      });
    }
  };

  return (
    <SbrDrawer
      title={title}
      width={800}
      onClose={onDrawerClose}
      visible={visible}
    >
      <Form
        onSubmit={handleSubmit}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <div className="public-block">
          <Spin spinning={loading}>
            <Form.Item hidden>
              {getFieldDecorator("id", {})(<Input placeholder="请输入名称" />)}
            </Form.Item>
            <Form.Item label="名称">
              {getFieldDecorator("name", {
                rules: [
                  {
                    required: true,
                    message: "请输入名称",
                  },
                  {
                    pattern: /^[\u4e00-\u9fa5\w]{1,20}$/,
                    message: '字符串长度范围: 1 - 20',
                  }  
                ],
              })(<Input placeholder="请输入名称" />)}
            </Form.Item>
            <Form.Item label="地址类型" onChange={radioChange}>
              {getFieldDecorator("type", {
                initialValue: 0,
                rules: [
                  {
                    required: true,
                    message: "请选择地址类型",
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={0} key={"0"}>
                    IP地址
                  </Radio>
                  <Radio value={1} key={"1"}>
                    地址范围
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {radioStaus == 0 ? (
              <Row style={{ marginRight: -333 }}>
                <Col span={10} offset={1}>
                  <Form.Item
                    label="IP地址"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator("ipaddr", {
                      rules: [
                        {
                          required: true,
                          message: "请输入地址",
                        },
                        {
                          pattern: /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/,
                          message: 'IP地址格式不正确',
                        } 
                      ],
                    })(<Input placeholder="请输入地址" />)}
                  </Form.Item>
                </Col>
                <Col span={1} />
                <Col span={5}>
                  <Form.Item>
                    {getFieldDecorator("mask", {
                      rules: [
                        {
                          required: true,
                          message: "请输入地址",
                        },
                      ],
                    })(<Input placeholder="请输入地址" />)}
                  </Form.Item>
                </Col>
              </Row>
            ) : (
              <Row style={{ marginRight: -333 }}>
                <Col span={10} offset={1}>
                  <Form.Item
                    label="IP地址"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator("ipaddr", {
                      rules: [
                        {
                          required: true,
                          message: "请输入地址",
                        },
                        {
                          pattern: /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/,
                          message: 'IP地址格式不正确',
                        } 
                      ],
                    })(<Input placeholder="请输入地址" />)}
                  </Form.Item>
                </Col>
                <Col span={1} />
                <Col span={5}>
                  <Form.Item>
                    {getFieldDecorator("endIp", {
                      rules: [
                        {
                          required: true,
                          message: "请输入地址",
                        },
                      ],
                    })(<Input placeholder="请输入地址" />)}
                  </Form.Item>
                </Col>
              </Row>
            )}
            <Form.Item label="备注">
              {getFieldDecorator("remark", {
                 rules: [{ max: 30, message: "备注长度不超过30字" },
                 { validator: validatorRemark },     
                ],
              })(
                <TextArea
                  placeholder="请输入备注"
                  autoSize={{ minRows: 2, maxRows: 6 }}
                />
              )}
            </Form.Item>
          </Spin>
        </div>

        <Row type="flex" justify="center" className="drawer_btns">
          <Button htmlType="submit" type="primary">
            保存
          </Button>
          <Button className="spacing_btn" onClick={onDrawerClose}>
            关闭
          </Button>
        </Row>
      </Form>
    </SbrDrawer>
  );
};
Add = Form.create()(Add);
export default forwardRef((props, ref) => <Add {...props} refInstance={ref} />);
