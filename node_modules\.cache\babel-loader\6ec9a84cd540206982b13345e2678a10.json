{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\ProgressModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\ProgressModal.vue", "mtime": 1750124694711}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}