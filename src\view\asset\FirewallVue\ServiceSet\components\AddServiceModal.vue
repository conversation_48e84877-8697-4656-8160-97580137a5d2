<template>
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>

        <el-form-item label="协议类型" prop="protocol">
          <el-select v-model="formData.protocol" placeholder="请选择协议类型">
            <el-option label="TCP" value="TCP"></el-option>
            <el-option label="UDP" value="UDP"></el-option>
            <el-option label="ICMP" value="ICMP"></el-option>
            <el-option label="ANY" value="ANY"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="端口" prop="port" v-if="formData.protocol !== 'ICMP'">
          <el-input v-model="formData.port" placeholder="请输入端口，多个端口用逗号分隔" />
        </el-form-item>

        <el-form-item label="端口范围" v-if="formData.protocol !== 'ICMP'">
          <el-row :gutter="10">
            <el-col :span="11">
              <el-input v-model="formData.startPort" placeholder="起始端口" />
            </el-col>
            <el-col :span="2" style="text-align: center">-</el-col>
            <el-col :span="11">
              <el-input v-model="formData.endPort" placeholder="结束端口" />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
            :rows="3"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addServiceSet, updateServiceSet, getServiceSetInfo } from '@/api/firewall/serviceSet'

export default {
  name: 'AddServiceModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      title: '新增服务集',
      formData: {
        id: null,
        name: '',
        protocol: 'TCP',
        port: '',
        startPort: '',
        endPort: '',
        remark: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5\w]{1,20}$/, message: '字符串长度范围: 1 - 20', trigger: 'blur' },
        ],
        protocol: [
          { required: true, message: '请选择协议类型', trigger: 'change' },
        ],
        port: [
          { validator: this.validatePort, trigger: 'blur' },
        ],
        remark: [
          { max: 30, message: '备注长度不超过30字', trigger: 'blur' },
        ],
      },
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
  },
  methods: {
    async initForm() {
      if (this.currentData && this.currentData.id) {
        this.title = '编辑服务集'
        this.loading = true
        try {
          const res = await getServiceSetInfo({ id: this.currentData.id })
          if (res.retcode === 0) {
            this.formData = {
              id: res.data.id,
              name: res.data.name,
              protocol: res.data.protocol,
              port: res.data.port,
              startPort: res.data.startPort,
              endPort: res.data.endPort,
              remark: res.data.remark,
            }
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('获取服务集信息失败')
        } finally {
          this.loading = false
        }
      } else {
        this.title = '新增服务集'
        this.formData = {
          id: null,
          name: '',
          protocol: 'TCP',
          port: '',
          startPort: '',
          endPort: '',
          remark: '',
        }
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    validatePort(rule, value, callback) {
      if (this.formData.protocol === 'ICMP') {
        callback()
        return
      }
      
      // 如果有端口范围，则端口字段可以为空
      if (this.formData.startPort && this.formData.endPort) {
        callback()
        return
      }
      
      // 如果没有端口范围，则必须填写端口
      if (!value && !this.formData.startPort && !this.formData.endPort) {
        callback(new Error('请输入端口或端口范围'))
        return
      }
      
      if (value) {
        // 验证端口格式（支持单个端口、多个端口用逗号分隔）
        const portPattern = /^(\d+)(,\d+)*$/
        if (!portPattern.test(value)) {
          callback(new Error('端口格式不正确'))
          return
        }
        
        // 验证端口范围
        const ports = value.split(',')
        for (let port of ports) {
          const portNum = parseInt(port)
          if (portNum < 1 || portNum > 65535) {
            callback(new Error('端口范围: 1-65535'))
            return
          }
        }
      }
      
      callback()
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 验证端口范围
          if (this.formData.startPort && this.formData.endPort) {
            const startPort = parseInt(this.formData.startPort)
            const endPort = parseInt(this.formData.endPort)
            if (startPort > endPort) {
              this.$message.error('起始端口不能大于结束端口')
              return
            }
          }

          this.loading = true
          try {
            let res
            if (this.title.includes('新增')) {
              res = await addServiceSet(this.formData)
            } else {
              res = await updateServiceSet(this.formData)
            }
            
            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    handleClose() {
      this.drawerVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin: 0 10px;
}
</style>
