<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="600px" :before-close="handleClose" @closed="onDialogClosed">
    <el-form ref="form" :model="formData" label-width="120px" :rules="rules">
      <el-form-item label="设备名称" prop="notes">
        <el-input v-model="formData.notes" placeholder="请输入设备名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="设备分组" prop="group_id">
        <ElTreeSelect
          v-model="formData.group_id"
          :options="groupData"
          placeholder="请选择分组"
          :props="{ label: 'groupName', children: 'childList', value: 'id' }"
          :clearable="true"
          :accordion="false"
        ></ElTreeSelect>
      </el-form-item>
      <el-form-item label="IP 地址" prop="ip">
        <el-input v-model="formData.ip" placeholder="请输入 IP 地址"></el-input>
      </el-form-item>
      <el-form-item label="设备重要程度" prop="importance">
        <el-select v-model="formData.importance" placeholder="请选择设备重要程度">
          <el-option label="高" :value="1"></el-option>
          <el-option label="中" :value="2"></el-option>
          <el-option label="低" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备位置" prop="position">
        <el-input v-model="formData.position" placeholder="请输入设备位置"></el-input>
      </el-form-item>
      <el-form-item label="安全责任人" prop="person_liable">
        <el-input v-model="formData.person_liable" placeholder="请输入安全责任人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contact">
        <el-input v-model="formData.contact" placeholder="请输入联系电话"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import ElTreeSelect from '@comp/SelectTree/SelectTree'
import { addSentineDevice } from '@/api/sentine/deviceManagement'

export default {
  name: 'AddDeviceModal',
  components: {
    ElTreeSelect,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '新建设备',
    },
    groupData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      formData: {
        notes: '',
        ip: '',
        group_id: '',
        importance: '',
        position: '',
        person_liable: '',
        contact: '',
      },
      rules: {
        notes: [
          { required: true, message: '请输入设备名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/, message: '请输入汉字、字母、数字、短横线或下划线', trigger: 'blur' },
        ],
        ip: [
          { required: true, message: '请输入 IP 地址', trigger: 'blur' },
          { pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/, message: '请输入正确的IP地址', trigger: 'blur' },
        ],
        group_id: [{ required: true, message: '请选择设备分组', trigger: 'change' }],
        importance: [{ required: true, message: '请选择设备重要程度', trigger: 'change' }],
        contact: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.resetFormData()
      }
    },
  },
  methods: {
    resetFormData() {
      this.formData = {
        notes: '',
        ip: '',
        group_id: '',
        importance: '',
        position: '',
        person_liable: '',
        contact: '',
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    onDialogClosed() {
      this.$refs.form && this.$refs.form.resetFields()
    },
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true

          const payload = {
            ...this.formData,
            category: 4, // 哨兵设备类型
          }

          try {
            const res = await addSentineDevice(payload)
            if (res.retcode === 0) {
              this.$message.success('新增成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.message || '新增失败')
            }
          } catch (error) {
            this.$message.error('新增失败')
          } finally {
            this.submitLoading = false
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
