<template>
  <el-dialog
    title="还原备份"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="备份信息">
        <div class="backup-info">
          <div class="info-row">
            <span class="label">备份名称：</span>
            <span class="value">{{ currentBackup.backupName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="label">备份类型：</span>
            <span class="value">{{ getBackupTypeText(currentBackup.backupType) }}</span>
          </div>
          <div class="info-row">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatTime(currentBackup.createTime) }}</span>
          </div>
          <div class="info-row">
            <span class="label">备份大小：</span>
            <span class="value">{{ currentBackup.fileSize || '-' }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="选择设备" prop="deviceIds">
        <el-tree
          ref="deviceTree"
          :data="deviceData"
          show-checkbox
          node-key="srcId"
          :props="treeProps"
          :check-strictly="false"
          @check="handleTreeCheck"
          :default-checked-keys="selectedDeviceIds"
        />
      </el-form-item>

      <el-form-item label="还原选项" prop="restoreOptions">
        <el-checkbox-group v-model="formData.restoreOptions">
          <el-checkbox value="config">配置文件</el-checkbox>
          <el-checkbox value="policy">策略规则</el-checkbox>
          <el-checkbox value="user">用户信息</el-checkbox>
          <el-checkbox value="log">日志设置</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-alert
        title="警告"
        type="warning"
        description="还原操作将覆盖目标设备的现有配置，请确认后再执行。建议在还原前先创建当前配置的备份。"
        show-icon
        :closable="false"
      />
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="danger" @click="handleSubmit" :loading="submitLoading">确认还原</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { restoreBackup, getDeviceList } from '@/api/firewall/backupRestore'
import dayjs from 'dayjs'

export default {
  name: 'RestoreModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentBackup: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      formData: {
        deviceIds: [],
        restoreOptions: ['config', 'policy'],
      },
      rules: {
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
        restoreOptions: [
          { required: true, message: '请选择还原选项', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadDeviceData()
      }
    },
  },
  methods: {
    initForm() {
      this.formData = {
        deviceIds: [],
        restoreOptions: ['config', 'policy'],
      }
      this.selectedDeviceIds = []
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      // 提取设备ID（type为'1'的节点）
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.type === '1') {
          deviceIds.push(node.srcId)
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    getBackupTypeText(type) {
      const typeMap = {
        'full': '完整备份',
        'config': '配置备份',
        'policy': '策略备份',
      }
      return typeMap[type] || '-'
    },
    formatTime(time) {
      if (!time) return '-'
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$confirm(
            '确认还原备份吗？此操作将覆盖目标设备的现有配置，且不可撤销！',
            '危险操作确认',
            {
              confirmButtonText: '确认还原',
              cancelButtonText: '取消',
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: '<p>还原操作风险提示：</p><ul><li>将覆盖目标设备的现有配置</li><li>操作不可撤销</li><li>建议先备份当前配置</li></ul>',
            }
          ).then(async () => {
            this.submitLoading = true
            try {
              const params = {
                backupId: this.currentBackup.id,
                deviceIds: this.formData.deviceIds.join(','),
                restoreOptions: this.formData.restoreOptions.join(','),
              }
              
              const res = await restoreBackup(params)
              if (res.retcode === 0) {
                this.$message.success('还原任务已创建，请稍后查看还原结果')
                this.$emit('on-submit')
                this.handleClose()
              } else {
                this.$message.error(res.msg)
              }
            } catch (error) {
              this.$message.error('还原失败')
            } finally {
              this.submitLoading = false
            }
          }).catch(() => {})
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.backup-info {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  .info-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      color: #666;
      font-weight: 500;
    }

    .value {
      color: #333;
    }
  }
}

:deep(.el-tree) {
  max-height: 300px;
  overflow: auto;
}

:deep(.el-alert) {
  margin-top: 16px;
}
</style>
