import request from '@/util/request'

/**
 * 授权管理-查询接口
 * @param params
 * @returns {Promise}
 */
export function getAuthList(params) {
  return request({
    url: '/dev/auth/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 授权管理-新增接口
 * @param data
 * @returns {Promise}
 */
export function addAuth(data) {
  return request({
    url: '/dev/auth/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 授权管理-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteAuth(data) {
  return request({
    url: '/dev/auth/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 授权管理-重新授权
 * @param data
 * @returns {Promise}
 */
export function reauthorize(data) {
  return request({
    url: '/dev/auth/reauthorize',
    method: 'post',
    data: data || {},
  })
}

/**
 * 授权管理-设备同步
 * @param data
 * @returns {Promise}
 */
export function syncAuthFromDevice(data) {
  return request({
    url: '/dev/auth/syncFromDevice',
    method: 'post',
    data: data || {},
  })
}
