import { stringify } from "qs";
import { request, requestBlob } from "@/utils/umiRequest.js";

//地址集-查询接口
export async function ipAddressSearch(params) {
  return request(`/dev/ipAddress/pages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//地址集-新增接口
export async function ipAddressAdd(params) {
  return request(`/dev/ipAddress/add`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//地址集-编辑接口
export async function ipAddressUpdate(params) {
  return request(`/dev/ipAddress/update`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//地址集-查看
export async function ipAddressInfo(params) {
  return request(`/dev/ipAddress/infor`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
// 导出
export async function exportResult(params) {
  return requestBlob(`/dev/inspectionResult/exportResult?${stringify(params)}`);
}
// 巡检查看
export async function inspectionInfo(params) {
  return request(`/dev/inspection/infor`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

// 巡检结果查看
export async function inspectionResultPages(params) {
  return request(`/dev/inspectionResult/pages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//地址集-设备集合
export async function addInspectionData(params) {
  return request(`/dev/device/all`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}



//地址集-删除接口
export async function ipAddressDelete(params) {
  return request(`/dev/ipAddress/delete`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//地址集-批量下发
export async function protocolIssued(params) {
    return request(`/dev/ipAddress/protocolIssued`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      data: JSON.stringify(params),
    });
  }
//地址集-同步设备协议
export async function syncFromDevice(params) {
    return request(`/dev/ipAddress/syncFromDevice`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      data: JSON.stringify(params),
    });
  }