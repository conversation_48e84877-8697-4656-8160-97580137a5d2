<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.fireName" clearable placeholder="设备名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="primary" @click="handleAdd">新增</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.fireName" clearable placeholder="设备名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <ElTreeSelect
                  v-model="queryInput.group_id"
                  :options="group_idOptions"
                  placeholder="请选择设备分组"
                  :props="{ label: 'groupName', children: 'childList', value: 'id' }"
                  :clearable="true"
                  :accordion="false"
                  @get-value="handleQuery"
                ></ElTreeSelect>
              </el-col>
              <el-col :span="6">
                <el-input v-model="queryInput.ip" clearable placeholder="设备IP" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.status" clearable placeholder="在线状态" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="在线" value="1"></el-option>
                  <el-option label="离线" value="0"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">哨兵设备管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="设备名称"></el-table-column>
          <el-table-column prop="category_text" label="设备类型"></el-table-column>
          <el-table-column prop="status" label="在线状态">
            <template slot-scope="scope">
              <span :class="scope.row.status === 1 ? 'status-online' : 'status-offline'">
                <img :src="scope.row.status === 1 ? onlineIcon : offlineIcon" style="margin-right: 3px" />
                {{ scope.row.status === 1 ? '在线' : '离线' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="syl_cpu" label="CPU率">
            <template slot-scope="scope">
              <el-progress
                v-if="scope.row.syl_cpu"
                :percentage="parseInt(scope.row.syl_cpu)"
                :stroke-width="8"
                :show-text="false"
                color="#52C41A"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="syl_nc" label="内存率">
            <template slot-scope="scope">
              <el-progress
                v-if="scope.row.syl_nc"
                :percentage="parseInt(scope.row.syl_nc)"
                :stroke-width="8"
                :show-text="false"
                color="#4C24ED"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="syl_disk" label="磁盘率">
            <template slot-scope="scope">
              <el-progress
                v-if="scope.row.syl_disk"
                :percentage="parseInt(scope.row.syl_disk)"
                :stroke-width="8"
                :show-text="false"
                color="#1373F1"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleConfig(scope.row)">配置</el-button>
                <el-button class="el-button--blue" type="text" @click="handleView(scope.row)">查看</el-button>
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
                <el-button class="el-button--blue" type="text" @click="handlePing(scope.row)">Ping</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>

    <!-- 新增设备对话框 -->
    <add-device-modal :visible.sync="addDeviceModalVisible" :group-data="groupData" @on-submit="handleAddDeviceSubmit"></add-device-modal>

    <!-- 配置对话框 -->
    <edit-config-modal
      :visible.sync="configModalVisible"
      :group-data="groupData"
      :current-config="currentConfig"
      @on-submit="handleConfigSubmit"
    ></edit-config-modal>
  </div>
</template>

<script>
import AddDeviceModal from './components/AddDeviceModal.vue'
import EditConfigModal from './components/EditConfigModal.vue'
import ElTreeSelect from '@comp/SelectTree/SelectTree'
import { getSentineDeviceList, batchDeleteSentineDevice, deleteSentineDevice, devicePing } from '@/api/sentine/deviceManagement'
import { searchGroupList } from '@/api/asset/hostguardiangroup'

import onlineIcon from '@/assets/IconFont/online.png'
import offlineIcon from '@/assets/IconFont/unonline.png'

export default {
  name: 'DeviceManagement',
  components: {
    AddDeviceModal,
    EditConfigModal,
    ElTreeSelect,
  },
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        fireName: '',
        group_id: '',
        ip: '',
        onlinStatus: '',
      },
      groupData: [],
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addDeviceModalVisible: false,
      configModalVisible: false,
      currentConfig: null,
      onlineIcon,
      offlineIcon,
      timer: null,
    }
  },
  mounted() {
    this.getSentineList()
    this.getGroupList()
    this.startTimer()
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    startTimer() {
      this.timer = setInterval(() => {
        this.getSentineList()
      }, 30000)
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    async getSentineList() {
      this.loading = true
      const payload = {
        _limit: this.pagination.pageSize,
        _page: this.pagination.currentPage,
        queryParams: this.buildQueryParams(),
        type: 4, // 哨兵设备类型
      }

      try {
        const res = await getSentineDeviceList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.items || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.fireName) params.fireName = this.queryInput.fireName
      if (this.queryInput.group_id) params.group_id = this.queryInput.group_id
      if (this.queryInput.ip) params.originIp = this.queryInput.ip
      if (this.queryInput.onlinStatus) params.onlinStatus = this.queryInput.onlinStatus
      return params
    },
    async getGroupList() {
      try {
        const res = await searchGroupList()
        if (res.retcode === 0) {
          this.groupData = res.data || []
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error('获取设备分组失败')
      }
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getSentineList()
    },
    handleReset() {
      this.queryInput = {
        fireName: '',
        group_id: '',
        ip: '',
        onlinStatus: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.addDeviceModalVisible = true
    },
    handleAddDeviceSubmit() {
      this.addDeviceModalVisible = false
      this.getSentineList()
    },
    handleConfig(record) {
      this.currentConfig = record
      this.configModalVisible = true
    },
    handleConfigSubmit() {
      this.configModalVisible = false
      this.getSentineList()
    },
    handleView(record) {
      if (record.status === 1) {
        window.open(`https://${record.ip}:8099`)
      } else {
        this.$message.error('设备不在线，无法查看!')
      }
    },
    handleDelete(record) {
      this.$confirm(`确认删除这条数据吗？`, '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteSentineDevice({ device_ids: [record.id] })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getSentineList()
            } else {
              this.$message.error(res.message)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm(`确认删除这${this.selectedRows.length}条数据吗？`, '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const deviceIds = this.selectedRows.map((row) => row.id)
            const res = await batchDeleteSentineDevice({ device_ids: deviceIds })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getSentineList()
            } else {
              this.$message.error(res.message)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    async handlePing(record) {
      try {
        const res = await devicePing({ ip: record.ip })
        if (res.retcode === 0) {
          if (res.data === 0) {
            this.$message.success('Ping成功')
          } else {
            this.$message.error('网络不通')
          }
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error('Ping失败')
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getSentineList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getSentineList()
    },
  },
}
</script>

<style lang="scss" scoped>
.status-online {
  color: #67c23a;
}

.status-offline {
  color: #f56c6c;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
