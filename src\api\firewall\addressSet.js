import request from '@/util/request'

/**
 * 地址集-查询接口
 * @param params
 * @returns {Promise}
 */
export function getAddressSetList(params) {
  return request({
    url: '/dev/ipAddress/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 地址集-新增接口
 * @param data
 * @returns {Promise}
 */
export function addAddressSet(data) {
  return request({
    url: '/dev/ipAddress/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 地址集-编辑接口
 * @param data
 * @returns {Promise}
 */
export function updateAddressSet(data) {
  return request({
    url: '/dev/ipAddress/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 地址集-查看
 * @param params
 * @returns {Promise}
 */
export function getAddressSetInfo(params) {
  return request({
    url: '/dev/ipAddress/infor',
    method: 'post',
    data: params || {},
  })
}

/**
 * 地址集-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteAddressSet(data) {
  return request({
    url: '/dev/ipAddress/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 地址集-批量下发
 * @param data
 * @returns {Promise}
 */
export function issueAddressSet(data) {
  return request({
    url: '/dev/ipAddress/protocolIssued',
    method: 'post',
    data: data || {},
  })
}

/**
 * 地址集-同步设备协议
 * @param data
 * @returns {Promise}
 */
export function syncAddressSetFromDevice(data) {
  return request({
    url: '/dev/ipAddress/syncFromDevice',
    method: 'post',
    data: data || {},
  })
}

/**
 * 地址集-获取设备集合
 * @param params
 * @returns {Promise}
 */
export function getDeviceList(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}
