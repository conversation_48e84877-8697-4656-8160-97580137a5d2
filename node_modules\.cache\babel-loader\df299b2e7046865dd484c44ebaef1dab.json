{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\components\\AddStrategyModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\components\\AddStrategyModal.vue", "mtime": 1750124198334}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}