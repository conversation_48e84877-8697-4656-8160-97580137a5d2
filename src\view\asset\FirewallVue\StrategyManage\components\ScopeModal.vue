<template>
  <el-dialog
    title="应用范围"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="选择设备" prop="deviceIds">
        <el-tree
          ref="deviceTree"
          :data="deviceData"
          show-checkbox
          node-key="srcId"
          :props="treeProps"
          :check-strictly="false"
          @check="handleTreeCheck"
          :default-checked-keys="selectedDeviceIds"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { setStrategyScope, getDeviceList } from '@/api/firewall/strategyManagement'

export default {
  name: 'ScopeModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      formData: {
        deviceIds: [],
      },
      rules: {
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadDeviceData()
      }
    },
  },
  methods: {
    initForm() {
      this.formData = {
        deviceIds: [],
      }
      this.selectedDeviceIds = []
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
          // 如果有当前策略的应用范围，设置默认选中
          if (this.currentData.appliedDevices) {
            this.selectedDeviceIds = this.currentData.appliedDevices.split(',')
          }
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      // 提取设备ID（type为'1'的节点）
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.type === '1') {
          deviceIds.push(node.srcId)
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const params = {
              id: this.currentData.id,
              deviceIds: this.formData.deviceIds.join(','),
            }
            
            const res = await setStrategyScope(params)
            if (res.retcode === 0) {
              this.$message.success('设置成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('设置失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

:deep(.el-tree) {
  max-height: 300px;
  overflow: auto;
}
</style>
