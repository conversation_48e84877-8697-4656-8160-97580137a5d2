import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  message,
  Form,
  Row,
  Col,
  Spin,
  Button,
  Select,
  Input,
  Radio,
} from "antd";
import SbrDrawer from "@/components/SbrDrawer";
import {tacticsInfo,  tacticsAdd, tacticsUpdate } from "../services";
import {industryProtocolSearch } from "../../../ResourceManagement/ControlProtocol/services";
import {fireRuleSearch } from "../../../ResourceManagement/ServiceSet/services";
import {ipAddressSearch } from "../../../ResourceManagement/AddressSet/services";
import core from '../../../../utils/validationUnit.js'
const { TextArea } = Input;
let Add = (props) => {
  const { refInstance, getSourceData } = props;
  const { getFieldDecorator, validateFields, setFieldsValue } = props.form;
  // 是否展示抽屉
  const [visible, setVisible] = useState(false);
  //标题
  const [title, setTitle] = useState('');
  // 查看数据
  const [record, setRecord] = useState({});
  const [loading, setLoading] = useState(false);
  const [ruleT, setRuleT] = useState(1);
  // 工业协议集合
  const [protocolList, setProtocolList] = useState([]);
  // 服务集合
  const [serviceList, setServiceList] = useState([]);
  // ip地址集合
  const [addressList, setAddressList] = useState([]);
  useImperativeHandle(refInstance, () => ({
    showDrawer,
  }));

  useEffect(() => {
    handleProtocol()
    handleService()
    handleAddress()

  }, []);

  const handleProtocol = async(value='') =>{
    try {
      // setProtocolList([]);
      setLoading(true);
      const res = await industryProtocolSearch({
        "pageIndex": 1,
        "pageSize": 50,
        name:value,
        // page:1,
        // rows:50
      });
      if (res.retcode == 0) {
        setProtocolList(res.data.rows);
        setLoading(false);
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  }
  const handleService = async(value='') =>{
    // setServiceList([])
    try {
      setLoading(true);
      const res = await fireRuleSearch({    
        name:value,
        "pageIndex": 1,
        "pageSize": 50,
      });
      if (res.retcode == 0) {
        setServiceList(res.data.rows);
        setLoading(false);
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  }

  const handleAddress = async(value='') =>{
    try {
      // setProtocolList([]);
      setLoading(true);
      const res = await ipAddressSearch({
        "pageIndex": 1,
        "pageSize": 50,
        name:value,
        // page:1,
        // rows:50
      });
      if (res.retcode == 0) {
        setAddressList(res.data.rows);
        setLoading(false);
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  }
   const handleSearchindustry =(value)=>{
    handleProtocol(value)
   }

   const handleSearchProtocol=(value)=>{
    handleService(value)
   }
   const handleSearchAddress =(value)=>{
    handleAddress(value)
   }
  // 打开抽屉
  const showDrawer = async (record = {}) => {
    setVisible(true);

    if (record.id) {
      setTitle('编辑策略资源');
      try {
        setLoading(true);
        const res = await tacticsInfo({ id: record.id });
        if (res.retcode == 0) {
          setFieldsValue({
            id: res.data.id,
            isOpenAppProtect: res.data.isOpenAppProtect,
            name: res.data.name,
            ruleType: res.data.ruleType,
            ipAddressIds:res.data.ipAddressIds,
            permitType: res.data.permitType,   
            remark: res.data.remark, 
            contrastIpId: res.data.contrastIpId,            
          });
          if(res.data.ruleType==1){
            setRuleT(1)
            setFieldsValue({industryProtocolId: res.data.industryProtocolId})
          }else{
            setRuleT(2)
            setFieldsValue({protocolId: res.data.protocolId})
          }         
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
      }
    }else{
      setRuleT(1)
      setTitle('新增策略资源');
    }
  };
  
   const handleType = (value) => {
    if(value==1){
      setFieldsValue({
        protocolId: '',
      });
      setRuleT(value)
    }else{
      setFieldsValue({
        industryProtocolId: '',
      });
      setRuleT(2)
    }
  
  };
 const  validatorName  =(rule, value, callback) =>{
  let reg = '/^[\\u4E00-\\u9FA5a-zA-Z0-9\\-_]{1,15}$/'
  if (core.regex(value, [reg]) && value.replace(/[\u4E00-\u9FA5]/g, '111').length <= 15) {
    callback()
  } else {
    callback(new Error('1-15个字母、数字、减号、中文、下划线的组合。'))
  }
  }
  const  validatorRemark  =(rule, value, callback) =>{
    let reg = '/^[\\u4E00-\\u9FA5a-zA-Z0-9\-\_\，\。\,\.\ \]+$/'
    if (core.regex(value, [reg])) {
      callback()
    } else {
      callback(new Error('只能输入字母、数字、减号、中文、下划线、空格、逗号、句号。'))
    }
    }
  
  //关闭抽屉
  const onDrawerClose = () => {
    setVisible(false);
  };
  // 提交
  const handleSubmit = (e) => {
    e.preventDefault();
    validateFields(async (err, values) => {
      if (!err) {
        let res 
        if(title.indexOf('新增')!=-1){
           res = await tacticsAdd(values);
        }else{
          res = await tacticsUpdate(values);
        }      
        if (res.retcode == 0) {
          message.success('操作成功');
          getSourceData();
          onDrawerClose();
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      }
    });
  };

  return <SbrDrawer title={title} width={800} onClose={onDrawerClose} visible={visible}>
      <Form onSubmit={handleSubmit} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
        <div className="public-block">
          <Spin spinning={loading}>
            <Form.Item hidden>
              {getFieldDecorator("id", {})(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
            <Form.Item label="名称">
              {getFieldDecorator("name", {
                  rules: [
                    {
                      required: true,
                      message: "请输入名称",
                    },
                    { validator: validatorName },                 
                  ],
              })(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
            <Form.Item label="应用防护">
              {getFieldDecorator("isOpenAppProtect", {
                initialValue: 0,
                rules: [
                  {
                    required: true,
                    message: "请选择应用防护",
                  },
                ],
              })(<Radio.Group onChange={() => {}}>
                  <Radio value={0} key={"0"}>
                    启用
                  </Radio>
                  <Radio value={1} key={"1"}>
                    禁用
                  </Radio>
                </Radio.Group>)}
            </Form.Item>
            <Form.Item label="源域">
                {getFieldDecorator("ipAddressIds", {
                  // initialValue: "",
                  rules: [
                    {
                      required: true,
                      message: "请选择源域",
                    },
                  ],
                })(<Select
                   showSearch 
                   style={{ width: "100%" }}
                   placeholder="请选择源域" 
                   onSearch={handleSearchAddress}
                   filterOption={false}
                >
                    {addressList.map((item) => (
                      <Select.Option value={item.id} key={item.id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>)}
              </Form.Item>
              <Form.Item label="目的域">
                {getFieldDecorator("contrastIpId", {
                  // initialValue: "",
                  rules: [
                    {
                      required: true,
                      message: "请选择目的域",
                    },
                  ],
                })(<Select
                   showSearch 
                   style={{ width: "100%" }}
                   placeholder="请选择目的域" 
                   onSearch={handleSearchAddress}
                   filterOption={false}
                >
                    {addressList.map((item) => (
                      <Select.Option value={item.id} key={item.id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>)}
              </Form.Item>
            <Form.Item label="规则类型">
              {getFieldDecorator("ruleType", {
                initialValue: 1,
                rules: [
                  {
                    required: true,
                    message: "请选择规则类型",
                  },
                ],
              })(<Select placeholder="请选择规则类型" onChange={handleType}>
                  <Option value={1}>工业协议</Option>
                  <Option value={0}>服务集</Option>
                </Select>)}
            </Form.Item>
            {ruleT == 1 ? <Form.Item label="工业协议">
                {getFieldDecorator("industryProtocolId", {
                  // initialValue: "",
                  rules: [
                    {
                      required: true,
                      message: "请选择工业协议",
                    },
                  ],
                })(<Select showSearch style={{ width: "100%" }} placeholder="请选择工业协议" 
                filterOption={false}
                onSearch={handleSearchindustry}
                                                           
                >
                    {protocolList.map((item) => (
                      <Select.Option value={item.id} key={item.id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>)}
              </Form.Item> : <Form.Item label="服务集">
                {getFieldDecorator("protocolId", {
                  // initialValue: "",
                  rules: [
                    {
                      required: true,
                      message: "请选择服务集",
                    },
                  ],
                })(<Select
                   showSearch 
                   style={{ width: "100%" }}
                   placeholder="请选择服务集" 
                   onSearch={handleSearchProtocol}
                   filterOption={false}
                >
                    {serviceList.map((item) => (
                      <Select.Option value={item.id} key={item.id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>)}
              </Form.Item>}
            <Form.Item label="访问控制">
              {getFieldDecorator("permitType", {
                initialValue: 'permit',
                rules: [
                  {
                    required: true,
                    message: "请选择访问控制",
                  },
                ],
              })(<Select placeholder="请选择访问控制">
                  <Option value='permit'>允许</Option>
                  <Option value='deny'>拒绝</Option>
                </Select>)}
            </Form.Item>
            <Form.Item label="备注">
              {getFieldDecorator("remark", {
                rules: [{ max: 30, message: "备注长度不超过30字" },
               { validator: validatorRemark },     
              ],
              })(<TextArea placeholder="请输入备注" autoSize={{ minRows: 2, maxRows: 6 }} />)}
            </Form.Item>
          </Spin>
        </div>

        <Row type="flex" justify="center" className="drawer_btns">
          <Button htmlType="submit" type="primary">
            保存
          </Button>
          <Button className="spacing_btn" onClick={onDrawerClose}>
            关闭
          </Button>
        </Row>
      </Form>
    </SbrDrawer>;
};
Add = Form.create()(Add);
export default forwardRef((props, ref) => <Add {...props} refInstance={ref} />);
