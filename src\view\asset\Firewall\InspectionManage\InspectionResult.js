import React, { createRef } from "react";
import { <PERSON><PERSON>,<PERSON>, But<PERSON> ,message, Table,Pagination,Spin } from 'antd';
import { inspectionResultPages} from "../../pages/ResourceManagement/AddressSet/services";
class App extends React.Component {
  state = { 
    currentPage:1,
    visible: this.props.visiableLook, 
    recordList:{},
    tableList:[],  //列表数据
    pageIndex: 1,
    pageSize: 10,
    loading:false
   };
  componentDidMount() {
    this.getInspectionData();
  }

  getInspectionData = async() => {
    try {
      this.setState({
        loading:true
      })
      const res = await inspectionResultPages({
        inspectionId:this.props.recordData.id,
        pageIndex:  this.state.pageIndex,
        pageSize:  this.state.pageSize,
      
      });
      if (res.retcode == 0) {
        this.setState({recordList:res.data});
      } else {
        message.error(res.msg);
      }
      this.setState({
        loading:false
      })
    } catch (err) {
      console.log(err);
    }
  };
    /**
   * 页面改变处理事件
   * @param pageNumber
   */
     handlePageChange = (pageNumber) => {
      this.setState(
        {
          pageIndex: pageNumber
        },
        () => {
          this.getInspectionData();
        }
      );
    };
    /**
   * 设备列表分页大小改变事件
   * @param current
   * @param pageSize
   */
     onShowSizeChange = (current, pageSize) => {
      this.setState(
        {
          pageIndex: current,
          pageSize: pageSize
        },
        () => {
          this.getInspectionData();
        }
      );
    };

      /**
   * 显示数据总数
   * @param total
   * @returns {string}
   */
  showTotal = (total) => {
    const {
      recordList
    } = this.state;
    return `总数据${recordList.total}条`;
  };
  
  columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "设备名称",
      dataIndex: "deviceName",
      key: "deviceName",
    },
    {
      title: "许可证状态",
      dataIndex: "liceneStatus",
      key: "liceneStatus",
      // render: (text, record) => (
      //   <>
      //     <span
      //       className={
      //         record.age === '0'
      //           ? 'redColor'
      //           : 'greenColor'                                    
      //       }
      //     >          
      //       {text}
      //     </span>
      //   </>
      // ),
    },
    {
      title: "CPU率",
      dataIndex: "cpuStatus",
      key: "cpuStatus",
      
    },
    {
      title: "内存率",
      dataIndex: "ramStatus",
      key: "ramStatus",
    },
    {
      title: "磁盘率",
      dataIndex: "diskStatus",
      key: "diskStatus",
    },   
    {
      title: "网络状态",
      dataIndex: "internetStatus",
      key: "internetStatus",
    },
  ];
  render() {
    const {visible,recordList,pageIndex,loading} =this.state;
  
    return (
      <div>      
        <Modal
        width={1200}
          title="查看巡检结果"
          visible={visible}
          maskClosable={false}
          // onOk={this.handleOk}
          onCancel={()=>this.props.handleCancelView()}
          footer={      
            <Button
              onClick={()=>this.props.handleCancelView()}
            >
              关闭
            </Button>}
        >
           <Spin spinning={loading}>
          <div className="public-block">
            <div className="public-block-details">       
              <div className={`tableBg`}>
              <Table dataSource={recordList.rows} columns={this.columns} pagination={false}/>
              <Row type="flex" justify="end">
          <Pagination
            style={{marginTop:10}}
            current={pageIndex}
            showQuickJumper
            // showSizeChanger
            onShowSizeChange={this.onShowSizeChange}
            total={recordList.total && parseInt(recordList.total)}
            showTotal={this.showTotal}
            onChange={this.handlePageChange}
          />
        </Row>
      </div>
            </div>
          </div>
          </Spin>
        </Modal>
      </div>
    )
  }
}
export default App