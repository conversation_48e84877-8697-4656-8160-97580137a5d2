import React, {Component, Fragment} from "react";
import {
    Form,
    Tabs,
} from "antd";
import {connect} from "dva";
import TabsChange from "./component/TabsChange"
import VirusLibrary from './component/VirusLibrary'


import styles from "./index.less";
const { TabPane } = Tabs;

@Form.create()
@connect(({}) => ({}))
export default class AlarmEvent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedRowKeys: [],
      activeKey: "0",
    };
  }

  componentWillUnmount() {}

  componentDidMount() {
  }

  handleTab = (key) => {};
  render() {
    return (
      <Fragment>
        <div className={styles.title}>升级管理</div>
          <Tabs
            onChange={this.handleTab}
          >
            <TabPane tab="病毒库更新" key="0">
              <VirusLibrary></VirusLibrary>
            </TabPane>
            <TabPane tab="升级记录管理" key="1">
              <TabsChange />
            </TabPane>
          </Tabs>
      </Fragment>
    );
  }
}
