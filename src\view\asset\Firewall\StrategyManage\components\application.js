import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { message, Row, Button, Spin, Col } from 'antd';
import SbrDrawer from '@/components/SbrDrawer';
import SbrTable from "@/components/SbrTable";
import moment from 'moment';
import { applyConfine } from '../services';

let View = (props) => {
  const { refInstance } = props;

  // 是否展示抽屉
  const [visible, setVisible] = useState(false);
  // 查看数据
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);

  useImperativeHandle(refInstance, () => ({
    showDrawer,
  }));

  useEffect(() => {}, []);

  // 打开抽屉
  const showDrawer = async (record = {}) => {
    setVisible(true);
    if (record.id) {
      try {
        setLoading(true);
        const res = await applyConfine({ id: record.id });
        if (res.retcode == 0) {
          setList(res.data);
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
      }
   };

    }; 
  //关闭抽屉
  const onDrawerClose = () => {
    setVisible(false);
    setList([]);
  };
  const columns = [
    {
      title: "序号",
      key: "key",
      dataIndex: "key",
      width: 50,
      render: (text, record, index) => {
        return `${index + 1}`;
      },
    },
    {
      title: "名称",
      key: "notes",
      dataIndex: "notes",
      ellipsis: true,
    },
    {
      title: "设备IP",
      key: "ip",
      dataIndex: "ip",
    },
    {
      title: "设备类型",
      key: "name",
      dataIndex: "name",
      render: (text, record, index) => {
        return '工控防火墙';
      },
    },
  ]
  return (
    <SbrDrawer title="应用范围" width={800} onClose={onDrawerClose} visible={visible}>
      <Spin spinning={loading}>
        <div className="public-block">
        <div className={`tableBg`}>
          <SbrTable
            columns={columns}
            scroll={false}
            tableList={list}
            style={{ wordWrap: "break-word", wordBreak: "break-all" }}
            rowKey={(record) => record.id}
            loading={loading}
            pagination={false}
          />
      </div>
        </div>
      </Spin>
      <Row type="flex" justify="center" className="drawer_btns">
        <Button className="spacing_btn" onClick={onDrawerClose}>
          关闭
        </Button>
      </Row>
    </SbrDrawer>
  );
};

export default forwardRef((props, ref) => <View {...props} refInstance={ref} />);
