import React, { Component, Fragment } from "react";
import { Card, Button, Table, Tabs, Pagination, Row, Col, Typography, Form, Select, Input, Modal, message, TreeSelect, Progress } from "antd";
import { connect } from "dva";
import EchartsTest from "../highcharts/commen";
// import AddDeviceModal from "./components/FirewallModal";
// import EditConfigModal from "./components/FirewallEdit";
import AddDeviceModal from "@/components/Device/AddDeviceModal";
import EditConfigModal from "@/components/Device/EditConfigModal";
import styles from "./index.less";
import online from "@/assets/IconFont/online.png";
import unonline from "@/assets/IconFont/unonline.png";
import Base64 from "crypto-js/enc-base64";
import Utf8 from "crypto-js/enc-utf8";
import { guid } from "@/utils/utils";

const { Column } = Table;
const Option = Select.Option;
const FormItem = Form.Item;
const { TabPane } = Tabs;
const { Text } = Typography;
const { TreeNode } = TreeSelect;
import custom from "../../config/custom.js";

const { primaryColor } = custom;

//防火墙角色映射关系 1=>管理员  3=>审计员
const fireWallRoleMap = {
  operator: 1,
  audit: 3,
};

@Form.create()
@connect(({ fireWall, loading }) => ({
  fireWall,
  loading: loading.models.fireWall,
}))
export default class FireWall extends Component {
  constructor(props) {
    super(props);
    this.newTabIndex = 0;
    this.state = {
      activeKey: "0",
      panes: [],
      timer: null,
      currentPage: 1,
      currentLimit: 10,
      currentUserPage: 1,
      currentUserLimit: 10,
      userTotal: 0,
      selectedRowKeys: [],
      selectedDevList: [],
      userSelectedRowKeys: [],
      searchValue: "",
      visible: false,
      remarkVisible: false,
      userVisible: false,
      currentData_id: null,
      currentDeviceId: 0,
      fireWallPwdList: [],
      isManualErr: false /*是否手工登录错误，错误则不保存用户名密码*/,
      isLocked: false /*是否锁定，如果锁定自动和手动都会通知，手动表示密码错误，自动表示要停止自动登录*/,
      iframeShow: false,
      currentConfig: null,
      curDevUserList: [],
      topoData: {} /*存储topo数据*/,
      groupData: [], //设备分组
    };
    this.receiveMessageFromIndex = this.receiveMessageFromIndex.bind(this);
  }

  componentWillUnmount() {
    const { timer } = this.state;
    //移除事件监听
    window.removeEventListener("message", this.receiveMessageFromIndex);
    timer && clearInterval(timer);
    const { dispatch } = this.props;
    dispatch({
      type: "fireWall/clearData",
    });
  }

  componentWillMount() {
    const timer = setInterval(() => this.getDeviceList(), 30000);
    this.setState({
      timer,
    });

    //获取缓存打开设备标签页
    let panes = localStorage.getItem("fireWallpanes");
    let fireWallUserPwdList = localStorage.getItem("fireWallUserPwdList");
    if (panes) {
      panes = JSON.parse(panes);
      this.setState({
        panes,
      });
    }
    if (fireWallUserPwdList) {
      fireWallUserPwdList = JSON.parse(fireWallUserPwdList);
      this.setState({
        fireWallPwdList: fireWallUserPwdList,
      });
    }
  }

  componentDidMount() {
    this.getDeviceList();
    this.getTopoData();
    this.getGroupList();
    window.addEventListener("message", this.receiveMessageFromIndex, false);
  }

  /**
   * 设备分组
   * @param account_id
   */
  getGroupList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: "GroupManagement/searchGroup",
      payload: {},
      callback: (res) => {
        if (res.retcode == 0) {
          this.setState({
            groupData: res.data,
          });
        } else {
          message.error(res.message);
        }
      },
    });
  };
  /**
   * 接受子页面postMessage消息监听方法
   * @param event
   */
  receiveMessageFromIndex = (event) => {
    let { fireWallPwdList, isLocked, curDevUserList } = this.state;
    let that = this;
    if (event != undefined) {
      const { status } = event.data;
      let fireWallPwdObj = {};
      if (status != "success") {
        if (event.data == "loadSuccess") {
          //设备登录页加载成功自动登录
          //获取对应ip在数组中的位置
          let resIndex = fireWallPwdList.findIndex((item) => item.ip == event.origin.substring(8));
          if (resIndex != -1 && !event.data["type"]) {
            that.handleAutoLogin(event.origin.substring(8), fireWallPwdList[resIndex]["UserNamePwd"]);
          }
        } else if (event.data["type"] && event.data.state === "fail") {
          null;
          // if (event.data.type === "auto") {
          //   message.error("设备密码已修改,请删除该用户信息重新手工登录!");
          // } else if (event.data["type"] == "lock") {
          //   that.setState({
          //     isLocked: true
          //   });
          // } else {
          //   that.setState({
          //     isManualErr: true
          //   });
          //   message.error("您输入的密码有误，请重新输入！");
          // }
        } else {
          //手工登录设备记录保存用户信息
          let isAdd = fireWallPwdList.some((item) => item.ip == event.origin.substring(8));
          if (!isLocked) {
            if (!isAdd) {
              fireWallPwdObj["ip"] = event.origin.substring(8);
              fireWallPwdObj["UserNamePwd"] = event.data;
              fireWallPwdList.push(fireWallPwdObj);
            } else {
              //获取对应ip在数组中的位置
              let resIndex = fireWallPwdList.findIndex((item) => item.ip == event.origin.substring(8));
              fireWallPwdList[resIndex]["UserNamePwd"] = event.data;
            }
            //缓存审计密码列表状态
            localStorage.setItem("fireWallUserPwdList", JSON.stringify(fireWallPwdList));
            that.setState({
              fireWallPwdList,
            });
          }
        }
      } else {
        //TODO 1设备登录成功添加角色信息到用户信息
        //TODO 2获取对应ip在数组中的位置
        let resIndex = fireWallPwdList.findIndex((item) => item.ip == event.origin.substring(8));
        let { accountId } = fireWallPwdList[resIndex];
        //TODO 3获取第二次请求成功登陆角色名
        if (fireWallPwdList[resIndex]) {
          fireWallPwdList[resIndex]["roleName"] = event.data.roleName;

          that.setState(
            {
              fireWallPwdList,
              isManualErr: false,
              isLocked: false,
            },
            () => {
              that.getIframeData(event.data.roleName, event.origin.substring(8));
            }
          );
        }

        //TODO 4 是否是第二次登录 是查找id不是为原id
        let account = curDevUserList.find((item) => item.account_name == event.data.userName);

        if (account) {
          accountId = account.id;
        }
        //调用登录日志接口记录日志
        accountId && that.loginSuccessSaveLog(accountId);
      }
    }
  };

  renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.childList) {
        return (
          <TreeNode title={item.groupName} key={item.id} value={item.id}>
            {this.renderTreeNodes(item.childList)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.groupName} key={item.id} value={item.id} />;
    });
  };
  /**
   * 自动登录成功发送请求保存日志
   * @param account_id
   */
  loginSuccessSaveLog = (account_id) => {
    const { dispatch } = this.props;
    dispatch({
      type: "fireWall/loginForLog",
      payload: {
        account_id: account_id,
      },
    });
  };

  /**
   * 处理自动登录逻辑
   * @param deviceIp
   * @param tokenData
   */
  handleAutoLogin = (deviceIp, tokenData) => {
    const childFrameObj = document.querySelectorAll("iframe");

    childFrameObj.forEach((item, index) => {
      let src = item.getAttribute("src");
      let ip = src.substring(8);
      if (deviceIp == ip) {
        const tokenArr = tokenData.split(":");
        item.contentWindow.postMessage(
          {
            username: tokenArr[0],
            pwd: tokenArr[1],
          },
          `https://${deviceIp}`
        );
      }
    });
  };

  /**
   * 封装登录设备成功处理逻辑
   */
  getIframeData = (roleName, deviceIp) => {
    const fireWallUserPwdList = localStorage.getItem("fireWallUserPwdList");
    if (fireWallUserPwdList) {
      const resIndex = JSON.parse(fireWallUserPwdList).findIndex((item) => item.ip == deviceIp);
      let loginValue = Base64.stringify(Utf8.parse(JSON.parse(fireWallUserPwdList)[resIndex]["UserNamePwd"]));
      const { dispatch } = this.props;
      const { currentDeviceId } = this.state;
      dispatch({
        type: "fireWall/addUser",
        payload: {
          auth: loginValue,
          device_id: currentDeviceId,
          account_role: fireWallRoleMap[roleName],
        },
      });
    }
  };

  /**
   * 获取设备列表数据
   */
  getDeviceList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: "fireWall/getData",
      payload: {
        _limit: this.state.currentLimit,
        _page: this.state.currentPage,
        queryParams: this.state.searchValue,
        type: 1,
      },
      callback: (res) => {
        //判断是否成功
        if (res.code == 0) {
          this.setState({
            selectedRowKeys: [],
          });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  /**
   *获取用户列表数据
   */
  getUserListData = (selfcallback) => {
    const { dispatch } = this.props;
    const { currentUserPage, currentUserLimit, currentDeviceId } = this.state;
    const that = this;
    dispatch({
      type: "fireWall/getUser",
      payload: {
        device_id: currentDeviceId,
        page: currentUserPage,
        per_page: currentUserLimit,
      },
      callback(res) {
        if (res.code == 0) {
          if (res.data.total) {
            that.setState({
              userTotal: res.data.total,
              curDevUserList: res.data.items,
            });
          }
        }
        if (selfcallback) selfcallback(res);
      },
    });
  };

  /**
   * 设备选择改变事件
   * @param selectedRowKeys
   */
  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedDevList: selectedRows.map((item) => item.ip),
    });
  };

  /**
   * 用户选择改变事件处理
   * @param selectedRowKeys
   */
  onUserSelectChange = (selectedRowKeys) => {
    this.setState({
      userSelectedRowKeys: selectedRowKeys,
    });
  };

  /**
   * 设备列表分页大小改变事件
   * @param current
   * @param pageSize
   */
  onShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        currentPage: current,
        currentLimit: pageSize,
      },
      () => {
        this.getDeviceList();
      }
    );
  };

  /**
   *用户列表分页大小改变事件
   * @param current
   * @param pageSize
   */
  onUserShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        currentUserPage: current,
        currentUserLimit: pageSize,
      },
      () => {
        this.getUserListData();
      }
    );
  };

  /**
   * 页面改变处理事件
   * @param pageNumber
   */
  handlePageChange = (pageNumber) => {
    this.setState(
      {
        currentPage: pageNumber,
      },
      () => {
        this.getDeviceList();
      }
    );
  };

  /**
   * 用户列表分页改变事件处理
   * @param pageNumber
   */
  handleUserPageChange = (pageNumber) => {
    this.setState(
      {
        currentUserPage: pageNumber,
      },
      () => {
        this.getUserListData();
      }
    );
  };

  /**
   * 显示总数逻辑
   * @param total
   * @returns {string}
   */
  showTotal = (total) => {
    const {
      fireWall: { fireWallData },
    } = this.props;
    return `总数据${fireWallData.total}条`;
  };

  /**
   * 显示数据总数
   * @returns {string}
   */
  showUserTotal = () => {
    const { userTotal } = this.state;
    return `总数据${userTotal}条`;
  };

  /**
   * 按条件查询设备
   */
  handleAdvancedSearch = () => {
    const { form } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        let searchValue = {};
        if (values.fireName) searchValue.fireName = values.fireName;
        if (values.group_id) searchValue.group_id = values.group_id;
        if (values.ip) searchValue.originIp = values.ip;
        if (values.onlinStatus) searchValue.onlinStatus = values.onlinStatus;
        this.setState(
          {
            searchValue: searchValue,
            currentPage: 1,
          },
          () => {
            this.getDeviceList();
          }
        );
      }
    });
  };
  handleReset = () => {
    const { resetFields } = this.props.form;
    resetFields();
  };

  /**
   * 设置添加模态框引用
   * @param modal
   * @constructor
   */
  showAddModal = (modal) => {
    this.AddModal = modal;
  };

  /**
   * 点击添加按钮显示模态框逻辑
   */
  handleAddClick = () => {
    const AddModal = this.AddModal;
    AddModal.onShow();
  };

  /**
   * 查看设备事件处理
   * @param record
   */
  handleLook = (record) => {
    const { panes } = this.state;
    const activeKey = record.ip;
    if (record.status == 1) {
      window.open(`https://${record.ip}`);
      // //添加标签页
      // panes.push({
      //   id: record.id,
      //   title: record.ip,
      //   content: `https://${record.ip}`,
      //   key: activeKey
      // });

      // let newPanes = [];
      // let newActiveKey = "";
      // for (let i = 0; i < panes.length; i++) { //去重添加已经打开的设备
      //   let flag = true;
      //   for (let j = 0; j < newPanes.length; j++) {
      //     if (panes[i].title == newPanes[j].title) {
      //       flag = false;
      //       newActiveKey = newPanes[j].key;
      //     }
      //   }

      //   if (flag) {
      //     newPanes.push(panes[i]);
      //   }

      // }

      // //缓存打开设备列表防止刷新页面消失
      // localStorage.setItem("fireWallpanes", JSON.stringify(newPanes));

      // this.setState({
      //   panes: newPanes,
      //   activeKey: newActiveKey ? newActiveKey : activeKey
      // });

      // //获取用户默认密码并修改缓存
      // this.getUserList(record.id, record.ip);

      // this.setState({
      //   currentDeviceId: record.id,
      //   iframeShow: true
      // });
    } else {
      message.error("设备不在线，无法查看!");
    }
  };

  /**
   * 根据设备id获取设备用户列表
   * @param deviceId
   */
  getUserList = (deviceId, deviceIp) => {
    const { dispatch } = this.props;
    let { fireWallPwdList } = this.state;
    let that = this;
    dispatch({
      type: "fireWall/getUser",
      payload: {
        device_id: deviceId,
        page: 1,
        per_page: 10,
      },
      callback: (res) => {
        if (res.code == 0) {
          if (res.data.items.length != 0) {
            const {
              fireWall: { getUserList, getAuthList },
            } = this.props;
            if (getUserList.length > 0) {
              let { default_account_auth } = res.data;
              default_account_auth.auth = default_account_auth.auth && Base64.parse(default_account_auth.auth).toString(Utf8);

              //获取对应ip在数组中的位置
              let resIndex = fireWallPwdList.findIndex((item) => item.ip == deviceIp);
              if (resIndex == -1) {
                let fireWallPwdObj = {};
                fireWallPwdObj["ip"] = deviceIp;
                fireWallPwdObj["UserNamePwd"] = default_account_auth.auth;
                fireWallPwdObj["accountId"] = default_account_auth.account_id;
                fireWallPwdList.push(fireWallPwdObj);
              } else {
                if (default_account_auth) {
                  fireWallPwdList[resIndex]["UserNamePwd"] = default_account_auth.auth;
                  fireWallPwdList[resIndex]["accountId"] = default_account_auth.account_id;
                }
              }
              that.setState({
                fireWallPwdList,
              });
            }
          }
        } else {
          message.error(res.message);
        }
      },
    });
  };

  /**
   * 取消按钮事件处理
   */
  handleCancelClick = () => {
    this.setState({
      visible: false,
      remarkVisible: false,
      userVisible: false,
    });
  };

  /**
   * 修改默认登录账号请求
   * @param params
   */
  modifyDefLoginAccount = (params) => {
    const { dispatch } = this.props;
    dispatch({
      type: "fireWall/modifyUser",
      payload: params,
      callback(res) {
        if (res.code == 0) {
          message.success("保存默认登录用户配置成功");
        } else {
          message.error(res.message);
        }
      },
    });
  };

  /**
   * 保存默认登录用户配置按钮事件处理
   */
  handleOkUser = () => {
    const {
      currentDeviceId,
      userSelectedRowKeys: [accountId],
    } = this.state;
    accountId &&
      this.modifyDefLoginAccount({
        deviceId: currentDeviceId,
        accountId,
      });
    this.setState({
      visible: false,
      remarkVisible: false,
      userVisible: false,
    });
  };

  /**
   * 配置模态框初始化
   * @param modal
   * @constructor
   */
  showConfigModal = (modal) => {
    this.ConfigModal = modal;
  };

  /**
   * 配置模态框显示逻辑
   * @param record
   */
  handleConfig = (record) => {
    const ConfigModal = this.ConfigModal;
    this.setState({
      currentData_id: record.id,
      currentConfig: record,
    });
    ConfigModal.onShow();
  };

  /**
   * 处理用户按钮点击事件
   * @param record
   */
  handleUser = (record) => {
    const that = this;
    this.setState(
      {
        userVisible: true,
        currentDeviceId: record.id,
      },
      () => {
        that.getUserListData((res) => {
          const {
            default_account_auth: { auth, account_id },
          } = res.data;
          that.setState({
            userSelectedRowKeys: [account_id],
          });
          // if (!auth) {
          //   message.error("请选择登陆用户并保存！");
          // }
        });
      }
    );
  };

  /**
   * 删除用户按钮点击事件处理
   * @param record
   */
  handleDeleteUser = (record) => {
    const { dispatch } = this.props;
    let { fireWallPwdList, userSelectedRowKeys } = this.state;
    dispatch({
      type: "fireWall/deleteUser",
      payload: {
        account_id: Number(record.id),
      },
      callback: (res) => {
        if (res.code == 0) {
          message.success("删除成功");
          fireWallPwdList = fireWallPwdList.filter((item) => item.accountId != record.id);
          localStorage.setItem("fireWallUserPwdList", JSON.stringify(fireWallPwdList));
          let accountId = userSelectedRowKeys ? userSelectedRowKeys[0] : null;
          userSelectedRowKeys = accountId && accountId == record.id ? [] : userSelectedRowKeys;
          this.setState(
            {
              fireWallPwdList,
              userSelectedRowKeys,
            },
            () => {
              this.getUserListData();
            }
          );
        } else {
          message.error(res.message);
        }
      },
    });
  };

  /**
   * tab标签页切换事件
   * @param activeKey
   */
  onChange = (activeKey) => {
    this.setState({ activeKey });
  };

  onEdit = (targetKey, action) => {
    let that = this;
    if (action == "remove") {
      let { panes, fireWallPwdList } = this.state;
      panes = panes.filter((item) => item.key != targetKey);
      fireWallPwdList = fireWallPwdList.filter((item) => item.ip != targetKey);
      this.handleAutoLoginOut(targetKey);
      localStorage.setItem("fireWallUserPwdList", JSON.stringify(fireWallPwdList));
      localStorage.setItem("fireWallpanes", JSON.stringify(panes));

      this.setState({
        panes,
        fireWallPwdList,
      });
    }
    this[action](targetKey);
  };

  /**
   * 处理自动登出逻辑
   * @type {{}}
   */
  handleAutoLoginOut = (deviceIp) => {
    const childFrameObj = document.querySelectorAll("iframe");
    childFrameObj.forEach((item, index) => {
      let src = item.getAttribute("src");
      let ip = src.substring(8);
      if (deviceIp == ip) {
        item.contentWindow.postMessage("logout", "*");
      }
    });
  };

  remove = (targetKey) => {
    let activeKey = this.state.activeKey;
    let lastIndex;
    this.state.panes.forEach((pane, i) => {
      if (pane.key === targetKey) {
        lastIndex = i - 1;
      }
    });
    const panes = this.state.panes.filter((pane) => pane.key !== targetKey);
    if (panes.length && activeKey === targetKey) {
      if (lastIndex >= 0) {
        activeKey = panes[lastIndex].key;
      } else {
        activeKey = panes[0].key;
      }
    }
    this.setState({
      panes,
      activeKey: this.state.panes.length == 1 ? "0" : activeKey,
    });
  };

  /**
   * Ping
   * @param record
   */
  handlePing = (record) => {
    const { dispatch } = this.props;
    dispatch({
      type: "fireWall/devicePing",
      payload: {
        ip: record.ip,
      },
      callback: (res) => {
        //判断是否成功
        if (res.code == 0) {
          if (res.data == 0) {
            message.success("Ping成功");
          } else {
            message.error("网络不通");
          }
        } else {
          message.error(res.message);
        }
      },
    });
  };
  /**
   * 设备配置提交事件处理
   * @param form
   */
  handleConifgSave = (form) => {
    const { dispatch } = this.props;
    const { currentData_id, currentConfig } = this.state;

    form.validateFields((err, values) => {
      if (!err) {
        if (
          currentConfig.importance != values.importance ||
          currentConfig.position != values.position ||
          currentConfig.person_liable != values.person_liable ||
          currentConfig.contact != values.contact ||
          currentConfig.group_id != values.group_id
        ) {
          const payload = {
            device_id: currentData_id,
            importance: parseInt(values.importance),
            position: values.position === undefined ? "" : values.position,
            person_liable: values.person_liable === undefined ? "" : values.person_liable,
            contact: values.contact === undefined ? "" : values.contact,
            group_id: values.group_id === undefined ? "" : values.group_id,
          };

          dispatch({
            type: "fireWall/editEquipment",
            payload: payload,
            callback: (res) => {
              if (res.code == 0) {
                message.success("编辑成功");
                this.setState(
                  {
                    remarkVisible: false,
                  },
                  (res) => {
                    this.getDeviceList();
                  }
                );
              } else {
                message.error(res.message);
              }
              this.ConfigModal.onHide();
            },
          });
        } else {
          message.error("配置未更改");
        }
      }
    });
  };

  /**
   * 批量删除设备
   * @param record
   */
  handleDeleteClick = (record) => {
    const { dispatch } = this.props;
    let selectedRowKeys = [];
    let selectedDevList = [];
    const that = this;
    if (record.ip) {
      selectedRowKeys.push(record.id);
      selectedDevList.push(record.ip);
    } else {
      selectedRowKeys = this.state.selectedRowKeys;
      selectedDevList = this.state.selectedDevList;
    }
    if (selectedRowKeys.length) {
      Modal.confirm({
        title: "删除",
        content: `确认删除这${selectedRowKeys.length}条数据吗？`,
        // content: `删除分组将删除与分组相关的数据，该操作无法撤销。`,
        okText: "确认",
        cancelText: "取消",
        onOk: () => {
          dispatch({
            type: "fireWall/DeleteData",
            payload: {
              device_ids: selectedRowKeys,
            },
            callback: (res) => {
              if (res.code == 0) {
                // that.setState({
                //   currentPage: 1
                // });
                that.getDeviceList();
                message.success("删除成功");
                // that.delDeviceNode(selectedDevList);
              } else {
                message.error(res.message);
              }
            },
          });
        },
      });
    } else {
      message.error("至少选中一条数据");
    }
  };

  /**
   * 添加设备节点功能
   * @param form
   */
  delDeviceNode(selectedDevList) {
    const that = this;

    that.getTopoData(() => {
      let { topoData } = this.state;
      //TODO 1 判断设备是否存在 存在就删除节点 生成连线边
      selectedDevList.forEach((item) => {
        let result =
          topoData.nodes.length != 0 &&
          topoData.nodes.find((val) => {
            return val.device_ip == item;
          });
        if (result) {
          topoData.nodes = topoData.nodes.filter((item) => result.id != item.id);
          topoData.edges = topoData.edges.filter((item) => result.id != item.target);
        }
      });

      //TODO 2 获取路由器列表
      let routerList = topoData.nodes.filter((item) => item.category == 3);

      //TODO 3 判断路由器是否孤立，如果孤立则删除。
      routerList.forEach((item) => {
        let isDel = topoData.edges.every((val) => val.source != item.id);
        if (isDel) {
          topoData.nodes = topoData.nodes.filter((value) => item.id != value.id);
        }
      });
      that.saveTopoData(topoData);
    });
  }

  /**
   * 处理添加设备确定事件
   * @param form
   */
  handleAddDevice = (form) => {
    const { dispatch } = this.props;
    const that = this;
    form.validateFields((err, values) => {
      if (!err) {
        that.AddModal.loading();
        const payload = {
          ...values,
          category: 1,
        };
        // this.getTopoData(form);
        dispatch({
          type: "fireWall/addEquipment",
          payload: payload,
          callback: (res) => {
            that.AddModal.unLoading();
            that.AddModal.onHide();
            if (res.code == 0) {
              message.success("新增成功");
              this.getDeviceList();
              // that.addDeviceNode(values);
            } else {
              message.error(res.message);
            }
          },
        });
      }
    });
  };

  /**
   * 获取拓扑数据
   */
  getTopoData(callback) {
    const { dispatch } = this.props;
    const that = this;
    dispatch({
      type: "fireWall/getTopoData",
      callback: (res) => {
        if (res.code == 0) {
          //判断是否成功
          that.setState(
            {
              topoData: res.data,
            },
            () => {
              if (callback) callback();
            }
          );
        } else {
          message.error(res.message);
        }
      },
    });
  }

  /**
   * 生成设备节点
   */
  generateNode(options) {
    return {
      id: guid(),
      type: "node",
      size: "50",
      shape: "koni-custom-node",
      color: "#69C0FF",
      labelOffsetY: 38,
      ...options,
    };
  }

  /**
   * 添加设备节点功能
   * @param form
   */
  addDeviceNode(values) {
    const that = this;

    that.getTopoData(() => {
      let { topoData } = this.state;
      //TODO 1 判断路由器是否存在,不存在生成路由器
      const { ip } = values;
      const iplist = ip.split(".");
      iplist.pop();
      let routerIp = iplist.join(".") + ".0";
      let isExist =
        (topoData.nodes.length != 0 &&
          topoData.nodes.some((val) => {
            return val.device_ip == routerIp;
          })) ||
        false;
      if (!isExist) {
        let newNode = that.generateNode({ device_ip: routerIp, category: 3, label: "路由器" });
        topoData.nodes.push(newNode);
      }

      //TODO 3 判断设备是否存在 不存在就生成设备节点 生成连线边
      let isDevExist =
        (topoData.nodes.length != 0 &&
          topoData.nodes.some((val) => {
            return val.device_ip == values.ip;
          })) ||
        false;
      if (!isDevExist) {
        const newNode = this.generateNode({
          label: "防火墙",
          category: 1,
          device_ip: values.ip,
        });
        topoData.nodes.push(newNode);
        const serverData = topoData.nodes.find((val) => val.device_ip == routerIp);
        topoData.edges.push({ id: guid(), source: serverData.id, target: newNode.id });
      }
      that.saveTopoData(topoData);
    });
  }

  /**
   * 调用保存拓扑数据接口
   * @param topoData
   */
  saveTopoData(topoData) {
    const { dispatch } = this.props;
    const that = this;
    dispatch({
      type: "fireWall/setTopoData",
      payload: {
        topology_text: topoData,
      },
      callback: (res) => {
        if (res.code == 0) {
          //判断是否成功
          that.getDeviceList();
        } else {
          message.error(res.message);
        }
      },
    });
  }

  render() {
    const {
      fireWall: { fireWallData, getUserList },
      loading,
    } = this.props;
    const fireWall = fireWallData.items;
    const { selectedRowKeys, currentPage, currentLimit, currentUserPage, userTotal, userVisible, panes, userSelectedRowKeys } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    const userRowSelection = {
      type: "radio",
      selectedRowKeys: userSelectedRowKeys,
      onChange: this.onUserSelectChange,
    };
    const { getFieldDecorator } = this.props.form;
    if (fireWall) {
      for (var i = 0; i < fireWall.length; i++) {
        fireWall[i] = { number: (currentPage - 1) * currentLimit + i + 1, ...fireWall[i] };
      }
    }

    return (
      <Fragment>
        <div className={styles.header}>
          <Tabs hideAdd onChange={this.onChange} activeKey={this.state.activeKey} type="editable-card" onEdit={this.onEdit}>
            <TabPane tab="设备列表" key="0" closable={false}>
              <div style={{ marginTop: 23, marginLeft: 30, marginRight: 30 }}>
                <Card
                  style={{
                    borderRadius: 8,
                  }}
                >
                  <Form className="searchBg" labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
                    <Row>
                      <Col span={6}>
                        <FormItem
                          // labelCol={{ span: 6 }}
                          // wrapperCol={{ span: 14 }}
                          label="设备名称:"
                        >
                          {getFieldDecorator("fireName", {
                            rules: [
                              {
                                pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,
                                message: "请输入汉字、字母、数字、短横线或下划线",
                              },
                            ],
                          })(<Input maxLength={50} placeholder="设备名称" />)}
                        </FormItem>
                      </Col>
                      <Col span={6}>
                        <FormItem label="设备分组:">
                          {getFieldDecorator("group_id", {})(
                            <TreeSelect style={{ width: "100%" }} dropdownStyle={{ maxHeight: 400, overflow: "auto" }} placeholder="请选择分组">
                              {this.renderTreeNodes(this.state.groupData)}
                            </TreeSelect>
                          )}
                        </FormItem>
                      </Col>
                      <Col span={6}>
                        <FormItem
                          // labelCol={{ span: 6 }}
                          // wrapperCol={{ span: 14 }}
                          label="设备IP:"
                        >
                          {getFieldDecorator("ip", {
                            rules: [
                              {
                                pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_\.]*$/,
                                message: "请输入汉字、字母、点、数字、短横线或下划线",
                              },
                            ],
                          })(<Input maxLength={50} placeholder="IP" />)}
                        </FormItem>
                      </Col>

                      <Col span={6}>
                        <FormItem
                          // labelCol={{ span: 8 }}
                          // wrapperCol={{ span: 14 }}
                          label="在线状态:"
                        >
                          {getFieldDecorator("onlinStatus", { initialValue: "" })(
                            <Select className={styles.contentWidth}>
                              <Option value="">全部</Option>
                              <Option value="1">在线</Option>
                              <Option value="0">离线</Option>
                            </Select>
                          )}
                        </FormItem>
                      </Col>

                      <Col span={6} className="searchBtn">
                        <Button type="primary" onClick={this.handleAdvancedSearch}>
                          查询
                        </Button>
                        <Button style={{ marginLeft: 15 }} onClick={this.handleReset}>
                          清除
                        </Button>
                      </Col>
                    </Row>
                  </Form>
                </Card>
                <div style={{ marginBottom: 20, marginTop: 20 }}>
                  <Button type="primary" style={{ marginRight: 15, borderRadius: 2 }} onClick={this.handleAddClick}>
                    + 新建
                  </Button>
                  <Button style={{ borderRadius: 2 }} onClick={this.handleDeleteClick}>
                    批量删除
                  </Button>
                </div>
                <Card
                  bordered={false}
                  style={{
                    borderRadius: 8,
                  }}
                  className="TableContainer"
                >
                  <Table
                    rowSelection={rowSelection}
                    className={styles.tablBar}
                    dataSource={fireWall}
                    loading={loading}
                    scroll={{ x: 1500 }}
                    rowKey={(record) => record.id}
                    pagination={false}
                    locale={{
                      emptyText: "暂无数据信息",
                    }}
                  >
                    <Column title="序号" dataIndex="number" width={50} fixed="left" key="number" />
                    <Column title="设备名称" dataIndex="notes" width={100} fixed="left" key="notes" />
                    <Column title="设备类型" dataIndex="category_text" key="category_text" />
                    <Column
                      title="在线状态"
                      dataIndex="status"
                      key="status"
                      render={(record) => {
                        if (record === 1) {
                          return (
                            <span>
                              <img src={online} style={{ marginRight: 3 }} />
                              在线
                            </span>
                          );
                        } else {
                          return (
                            <span>
                              <img src={unonline} style={{ marginRight: 3 }} />
                              离线
                            </span>
                          );
                        }
                      }}
                    />
                    <Column
                      title="CPU率"
                      dataIndex="syl_cpu"
                      key="syl_cpu"
                      render={(text) => {
                        return text ? (
                          <Progress
                            percent={Number(text.slice(0, text.length - 1))}
                            status="active"
                            style={{ width: 100 }}
                            size="small"
                            strokeColor="#52C41A"
                          />
                        ) : (
                          ""
                        );
                      }}
                    />
                    <Column
                      title="内存率"
                      dataIndex="syl_nc"
                      key="syl_nc"
                      render={(text) => {
                        return text ? (
                          <Progress
                            percent={Number(text.slice(0, text.length - 1))}
                            status="active"
                            style={{ width: 100 }}
                            size="small"
                            strokeColor="#4C24ED"
                          />
                        ) : (
                          ""
                        );
                      }}
                    />
                    <Column
                      title="磁盘率"
                      dataIndex="syl_disk"
                      key="syl_disk"
                      render={(text) => {
                        return text ? (
                          <Progress
                            percent={Number(text.slice(0, text.length - 1))}
                            status="active"
                            style={{ width: 100 }}
                            size="small"
                            strokeColor="#1373F1"
                          />
                        ) : (
                          ""
                        );
                      }}
                    />

                    <Column
                      title="操作"
                      dataIndex="action"
                      key="action"
                      fixed="right"
                      width={200}
                      render={(text, record) => (
                        <div style={{ color: primaryColor }}>
                          <Col>
                            <a
                              style={{ borderRadius: 2, color: primaryColor }}
                              onClick={() => {
                                this.handleConfig(record);
                              }}
                            >
                              配置
                            </a>
                            <a
                              style={{ borderRadius: 2, marginLeft: 30, color: primaryColor }}
                              onClick={() => {
                                this.handleLook(record);
                              }}
                            >
                              查看
                            </a>
                          </Col>
                          <Col style={{ marginTop: 10 }}>
                            <a
                              style={{ borderRadius: 2, color: primaryColor }}
                              onClick={() => {
                                this.handleDeleteClick(record);
                              }}
                            >
                              删除
                            </a>
                            <a
                              style={{ borderRadius: 2, marginLeft: 30, color: primaryColor }}
                              onClick={() => {
                                this.handlePing(record);
                              }}
                            >
                              Ping
                            </a>
                          </Col>
                        </div>
                      )}
                    />
                  </Table>
                  <Row type="flex" justify="end" style={{ marginTop: 20 }}>
                    <Pagination
                      current={currentPage}
                      showQuickJumper
                      showSizeChanger
                      onShowSizeChange={this.onShowSizeChange}
                      total={fireWallData.total && parseInt(fireWallData.total)}
                      showTotal={this.showTotal}
                      onChange={this.handlePageChange}
                    />
                  </Row>
                </Card>

                <AddDeviceModal groupData={this.state.groupData} wrappedComponentRef={this.showAddModal} saveOnClick={this.handleAddDevice} />

                <EditConfigModal
                  wrappedComponentRef={this.showConfigModal}
                  groupData={this.state.groupData}
                  currentConfig={this.state.currentConfig}
                  saveOnClick={this.handleConifgSave}
                />

                <Modal
                  visible={userVisible}
                  title={[<Text strong>已保存用户</Text>, <Text type="secondary">请选择登陆用户并保存</Text>]}
                  okText="保存"
                  destroyOnClose
                  centered
                  onCancel={this.handleCancelClick}
                  onOk={this.handleOkUser}
                  width={650}
                  maskClosable={false}
                >
                  <Table
                    rowSelection={userRowSelection}
                    dataSource={getUserList}
                    loading={loading}
                    rowKey={(record) => record.id}
                    pagination={false}
                    className={styles.tablBar}
                    locale={{
                      emptyText: "暂无数据信息",
                    }}
                  >
                    <Column title="序号" dataIndex="key" key="key" width={60} render={(text, record, index) => `${index + 1}`} />
                    <Column title="用户名" dataIndex="account_name" key="account_name" />
                    <Column title="用户类型" dataIndex="account_role_text" key="account_role_text" />
                    <Column
                      title="操作"
                      dataIndex="datetime"
                      key="datetime"
                      render={(text, record) => (
                        <a
                          style={{ borderRadius: 2, color: primaryColor }}
                          onClick={() => {
                            this.handleDeleteUser(record);
                          }}
                        >
                          删除
                        </a>
                      )}
                    />
                  </Table>

                  <Row type="flex" justify="end" style={{ marginTop: 20 }}>
                    <Pagination
                      current={currentUserPage}
                      showQuickJumper
                      showSizeChanger
                      onShowSizeChange={this.onUserShowSizeChange}
                      total={parseInt(userTotal)}
                      showTotal={this.showUserTotal}
                      onChange={this.handleUserPageChange}
                    />
                  </Row>
                </Modal>
              </div>
            </TabPane>

            {panes &&
              panes.map((pane) => (
                <TabPane tab={pane.title} key={pane.key}>
                  <iframe src={pane.content} width="100%" height="700" frameBorder="0" />
                </TabPane>
              ))}
          </Tabs>
        </div>
      </Fragment>
    );
  }
}
