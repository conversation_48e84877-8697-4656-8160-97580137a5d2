import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";
//工业协议集记录-查询
export async function tacticsPages(params) {
  return request(`/dev/tactics/records`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//工业协议集记录-删除接口
export async function tacticsDeleteR(params) {
  return request(`/dev/tactics/deleteRecord`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}









