<template>
  <el-dialog
    title="添加新设备"
    :visible.sync="dialogVisible"
    width="650px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入设备名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="设备分组" prop="groupId">
        <el-tree-select
          v-model="formData.groupId"
          :data="groupData"
          :props="treeProps"
          placeholder="请选择分组"
          style="width: 100%"
          :check-strictly="true"
        />
      </el-form-item>

      <el-form-item label="设备IP" prop="originIp">
        <el-input
          v-model="formData.originIp"
          placeholder="请输入IP地址"
          @blur="validateIP"
        />
        <span v-if="showIPError" style="color: red; font-size: 12px;">IP地址不能为空</span>
      </el-form-item>

      <el-form-item label="端口" prop="port">
        <el-input
          v-model="formData.port"
          placeholder="请输入端口"
          type="number"
        />
      </el-form-item>

      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="formData.username"
          placeholder="请输入用户名"
        />
      </el-form-item>

      <el-form-item label="密码" prop="password">
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </el-form-item>

      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="formData.deviceType" placeholder="请选择设备类型">
          <el-option label="防火墙" value="firewall"></el-option>
          <el-option label="路由器" value="router"></el-option>
          <el-option label="交换机" value="switch"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入备注"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addFirewallDevice, updateFirewallDevice } from '@/api/firewall/deviceManagement'

export default {
  name: 'AddDeviceModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
    groupData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      showIPError: false,
      formData: {
        id: null,
        name: '',
        groupId: '',
        originIp: '',
        port: '443',
        username: '',
        password: '',
        deviceType: 'firewall',
        remark: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入设备名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/, message: '请输入汉字、字母、数字、短横线或下划线', trigger: 'blur' },
        ],
        groupId: [
          { required: true, message: '请选择设备分组', trigger: 'change' },
        ],
        originIp: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { pattern: /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/, message: 'IP地址格式不正确', trigger: 'blur' },
        ],
        port: [
          { required: true, message: '请输入端口', trigger: 'blur' },
          { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: '端口范围: 1-65535', trigger: 'blur' },
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
        ],
        deviceType: [
          { required: true, message: '请选择设备类型', trigger: 'change' },
        ],
      },
      treeProps: {
        children: 'childList',
        label: 'groupName',
        value: 'id',
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    isEdit() {
      return this.currentData && this.currentData.id
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
  },
  methods: {
    initForm() {
      if (this.isEdit) {
        this.formData = {
          id: this.currentData.id,
          name: this.currentData.fireName || '',
          groupId: this.currentData.groupId || '',
          originIp: this.currentData.originIp || '',
          port: this.currentData.port || '443',
          username: this.currentData.username || '',
          password: this.currentData.password || '',
          deviceType: this.currentData.deviceType || 'firewall',
          remark: this.currentData.remark || '',
        }
      } else {
        this.formData = {
          id: null,
          name: '',
          groupId: '',
          originIp: '',
          port: '443',
          username: '',
          password: '',
          deviceType: 'firewall',
          remark: '',
        }
      }
      this.showIPError = false
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    validateIP() {
      this.showIPError = this.formData.originIp === '...'
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid && !this.showIPError) {
          this.submitLoading = true
          try {
            let res
            const submitData = {
              ...this.formData,
              fireName: this.formData.name, // 适配后端字段名
            }
            
            if (this.isEdit) {
              res = await updateFirewallDevice(submitData)
            } else {
              res = await addFirewallDevice(submitData)
            }
            
            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
