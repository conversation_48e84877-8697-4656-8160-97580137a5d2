import React, { Component, Fragment } from "react";
import {
  Card,
  Button,
  Table,
  Alert,
  Pagination,
  Row,
  Col,
  Form,
  Select,
  DatePicker,
  Icon,
  Input,
  Modal,
  message,
} from "antd";



import moment from "moment";
import { connect } from "dva";
import styles from "./index.less";
import locale from "antd/es/date-picker/locale/zh_CN";
import custom from "../../config/custom.js";
// import SystemDateModal from "./component/SystemDateModal";
import { downloadFile } from '@/utils/utils';
import BatchInspection from "./BatchInspection";
import InspectionResult from "./InspectionResult";
import ViewDetail from "./ViewDetail";

import { exportResult } from "../../pages/ResourceManagement/AddressSet/services";
const { Column } = Table;
const Option = Select.Option;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;


const { primaryColor } = custom;
@Form.create()
@connect(({ }) => ({
}))
export default class InspectionManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentPage: 1,
      excelCurrPage: 1,
      currentPageSize: 10,
      searchValue: "",
      selectedRowKeys: [],
      excelSelRows: [] /*用于手工选导出*/,
      search: "",
      startValue: null,
      endValue: null,
      endOpen: false,
      authority: "",
      headerShow: false,
      singleChoice: true,
      newSelectedRowKeys: [],
      totalRecords: 0,
      currentSelNum: 0,
      curPageSelNum: 0 /*本页选中数量*/,
      notSelectedRowKeys: [],
      visiableInspection: false,
      visiableLook: false,
      inspectionList:[],
      currentRecord:{},
      visiableDetail:false,
      recordData:{},
      loading:false
    };
  }
  componentWillUnmount() {
    const { dispatch } = this.props;
    dispatch({
      type: "systemLog/clearData",
      payload: {},
    });
  }
  componentDidMount() {
    this.getInspectionData();
  }

  /**
   *获取巡检管理数据列表
   */
  getInspectionData = () => {
    const { dispatch } = this.props;
    this.setState({
      loading:true
    })
    dispatch({
      type: "inspectionManagement/searchData",
      payload: {
        pageSize: this.state.currentPageSize,
        pageIndex: this.state.currentPage,
        startTime: this.state.searchValue.startTime,
        endTime: this.state.searchValue.endTime,
      },
      callback: (res) => {
        //判断是否成功
        if (res.retcode == 0) {
          this.setState({
            selectedRowKeys: [],
            inspectionList:res.data,
          });
        } else {
          message.error(res.message);
        }
        this.setState({
          loading:false
        })
      },
    });
  };

  /**
   * 表格选中项发生变化时的回调
   * @param selectedRowKeys
   * @param selectedRows
   */
  onSelectChange = (selectedRowKeys, selectedRows) => {
    const {
      singleChoice,
      newSelectedRowKeys,
      excelSelRows,
      selectedRowKeys: currentSelectedRowKeys,
    } = this.state;

    /*用于删除的数据*/
    selectedRows.forEach((value) => {
      let isExist = newSelectedRowKeys.every((item) => item != value.id);
      if (isExist) {
        newSelectedRowKeys.push(value.id);
      }
    });

    /*用户excel导出的数据*/
    selectedRows.forEach((value) => {
      let isExist = excelSelRows.every((item) => item.id != value.id);
      if (isExist) {
        excelSelRows.push(value);
      }
    });

    this.setState({
      selectedRowKeys,
      newSelectedRowKeys /*设置新的选中行*/,
      excelSelRows /*设置选中要导出的行*/,
      currentSelNum: selectedRowKeys.length /*设置当前手工选中数量*/,
      headerShow:
        singleChoice == false && currentSelectedRowKeys.length == 0
          ? false
          : true,
    });
  };

  /**
   * 表格用户手动选择/取消选择所有行的回调
   */
  onSelectAll = (selected, selectedRows, changeRows) => {
    let ids = changeRows.map((item) => item.id);
    let {
      notSelectedRowKeys,
      singleChoice,
      currentSelNum,
      headerShow,
      excelSelRows,
    } = this.state;

    /*获取未选中的id列表*/
    if (!singleChoice) {
      if (!selected) {
        /*反选操作*/

        this.setState(
          {
            notSelectedRowKeys: notSelectedRowKeys.concat(ids),
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum - changeRows.length,
            });
          }
        );
      } else {
        /*选中操作*/

        let res = notSelectedRowKeys;
        ids.forEach((value) => {
          res = res.filter((item) => value !== item);
        });

        this.setState(
          {
            headerShow:
              selectedRows.length === changeRows.length ? true : headerShow,
            notSelectedRowKeys: res,
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum + changeRows.length,
            });
          }
        );
      }
    } else {
      if (!selected) {
        let newSelRow = excelSelRows.filter(
          (item) => !changeRows.some((value) => value.id == item.id)
        );
        this.setState({
          excelSelRows: newSelRow,
        });
      }
    }

    //设置本页选中多少条
    this.setState({
      curPageSelNum: selectedRows.length,
    });
  };

  /**
   * 表格用户手动选择/取消选择某行的回调
   * @param record
   * @param selected
   * @param selectedRows
   * @param nativeEvent
   */
  onSelect = (record, selected, selectedRows) => {
    let {
      notSelectedRowKeys,
      singleChoice,
      currentSelNum,
      excelSelRows,
    } = this.state;

    if (!singleChoice) {
      /*获取未选中的id列表*/
      if (!selected) {
        const { id } = record;
        this.setState(
          {
            notSelectedRowKeys: notSelectedRowKeys.concat(id),
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum - 1,
            });
          }
        );
      } else {
        const { id } = record;
        this.setState(
          {
            notSelectedRowKeys: notSelectedRowKeys.filter(
              (item) => item !== id
            ),
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum + 1,
            });
          }
        );
      }
    } else {
      if (!selected) {
        const { id } = record;
        let newSelRow = excelSelRows.filter((item) => item.id != id);
        this.setState({
          excelSelRows: newSelRow,
        });
      }
    }

    //设置本页选中多少条
    this.setState({
      curPageSelNum: selectedRows.length,
    });
  };

  /**
   * 删除巡检数据
   */
  handleDeleteClick = (record) => {
    const { dispatch } = this.props;
    const {
      notSelectedRowKeys,
      currentSelNum,
      singleChoice,
    } = this.state;
    let selectedRowKeys = [];
    const that = this;
    if (record.id) {
      selectedRowKeys.push(record.id);
    } else {
      selectedRowKeys = this.state.selectedRowKeys;
    }
    if (selectedRowKeys.length) {
      Modal.confirm({
        title: "删除",
        content: `确认删除选中的巡检数据吗？`,
        okText: "确认",
        cancelText: "取消",
        onOk: () => {
          return new Promise((resolve, reject) => {
            let params = {
              ids: selectedRowKeys.join(),
            };
            dispatch({
              type: "inspectionManagement/deleteInspectionData",
              payload: params,
              callback: (res) => {
                if (res.retcode == 0) {
                  this.setState(
                    {
                      currentPage: 1,
                      excelSelRows: [],
                      selectedRowKeys: [],
                      currentSelNum: 0,
                    },
                    () => {
                      message.success("删除成功");
                      this.getInspectionData();
                    }
                  );
                } else {
                  message.error(res.message);
                }
                resolve();
              },
            });
          }).catch((err) => {
            console.log(err);
          });
        },
      });
    } else {
      message.error("至少选中一条数据");
    }
  };

  AddModalBoxRef = (modal) => {
    this.addAccountModal = modal;
  };

  /**
   * 导出excel表格
   */
  downloadExcel = async(record) => {
    try {
      const res = await exportResult({ id: record.id });
      downloadFile(res);
    } catch (err) {
      console.log(err);
    }
  };

  /**
   * 导出操作保存日志
   */
  exportExcelSaveLog = () => {
    const { dispatch } = this.props;
    let { searchValue, currentPageSize } = this.state;
    const that = this;
    dispatch({
      type: "systemLog/getData",
      payload: {
        per_page: currentPageSize,
        page: 1,
        export: 1,
        queryParams: searchValue,
      },
    });
  };
  handleReset = () => {
    const { resetFields } = this.props.form;
    this.setState({
      startValue:null
    },()=>{
      resetFields();
    })
  };


  // 查看
  handleLookDetail = (record) =>{
    this.setState({
      visiableDetail:true,
      recordData:record
    })
  }
  // 巡检结果
  handleResult= (record) =>{
    this.setState({
      visiableLook:true,
      recordData:record
    })
  }
  handleCancelView = () => {
    this.setState({
      visiableLook: false,
      visiableDetail:false,
    })
  }
  /**
   * 页面改变事件处理
   * @param pageNumber
   */
  handlePageChange = (pageNumber) => {
    const { singleChoice, selectedRowKeys } = this.state;
    const that = this;
    if (singleChoice) {
      /*当前不是分页全选状态*/
      this.setState(
        {
          currentPage: pageNumber,
        },
        () => {
          this.getInspectionData();
        }
      );
    } else {
      /*当前是分页全选状态*/
      this.setState(
        {
          currentPage: pageNumber,
        },
        () => {
          this.getInspectionData(function (res) {
            that.setState({
              selectedRowKeys: selectedRowKeys.concat(
                res.items.map((item) => item.id)
              ),
            });
          });
        }
      );
    }
  };

  /**
   * 页面记录数改变事件处理
   * @param current
   * @param pageSize
   */
  onShowSizeChange = (current, pageSize) => {
    const { selectedRowKeys, singleChoice } = this.state;
    let that = this;
    if (singleChoice) {
      this.setState(
        {
          currentPage: 1,
          currentPageSize: pageSize,
        },
        () => {
          this.getInspectionData();
        }
      );
    } else {
      this.setState(
        {
          currentPage: 1,
          currentPageSize: pageSize,
        },
        () => {
          this.getInspectionData(function (res) {
            let curSelRowKeys = selectedRowKeys.concat(
              res.items.map((item) => item.id)
            );
            //合并新旧选中列表并去重设置到状态变量中
            that.setState({
              selectedRowKeys: [...new Set(curSelRowKeys)],
            });
          });
        }
      );
    }
  };

  /**
   * 显示数据总数
   * @param total
   * @returns {string}
   */
  showTotal = (total) => {
    const {
      inspectionList
    } = this.state;
    return `总数据${inspectionList.totalElements}条`;
  };

  /**
   * 条件查询事件处理
   */
  handleSearch = () => {
    const { form } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        let searchValue = {};
        if (this.state.startValue) {
          let startValue = this.state.startValue;
          let endValue = this.state.endValue;
          searchValue.startTime = startValue;
          searchValue.endTime = endValue;
        }else{
          searchValue.startTime = null;
          searchValue.endTime = null;
        }
        this.setState(
          {
            searchValue: searchValue,
            currentPage: 1,
            headerShow: false,
          },
          () => {
            this.getInspectionData();
          }
        );
      }
    });
  };

  /**
   * 禁用选择日期的处理
   * @param current
   * @returns {*|boolean}
   */
  disabledDate = (current) => {
    var oldTime = new Date("1970/1/1 00:00:01").getTime();
    return current && current < oldTime;
  };

  disabledRangTime = (startValue) => {
    return {
      disabledHours: () => range(0, 24).splice(4, 20),
      disabledMinutes: () => range(30, 60),
      disabledSeconds: () => [55, 56],
    };
  };

  /**
   * 选择开始结束日期改变事件处理
   * @param dates
   * @param dateStrings
   */
  onRangChange = (dates, dateStrings) => {
    this.setState({
      startValue: dateStrings[0],
      endValue: dateStrings[1],
    });
  };

  /**
   * 禁用开始选择日期处理
   * @param startValue
   * @returns {boolean}
   */
  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue;
    var oldTime = new Date("1970/1/1 20:11:11").getTime(); //得到毫秒数
    if (!startValue || !endValue) {
      //如果没有选择结束日期，则选择开始日期时，开始日期不能大于今天
      return startValue.valueOf() < oldTime; //大于今天的日期一律返回true，禁止选择
    }
    return startValue.valueOf() > endValue.valueOf();
  };

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.valueOf() <= startValue.valueOf();
  };

  onChange = (field, value) => {
    this.setState({
      [field]: value,
    });
  };

  onStartChange = (value) => {
    this.onChange("startValue", value);
  };

  onEndChange = (value) => {
    this.onChange("endValue", value);
  };

  handleStartOpenChange = (open) => {
    if (!open) {
      this.setState({ endOpen: true });
    }
  };

  handleEndOpenChange = (open) => {
    this.setState({ endOpen: open });
  };

  //   新建巡检
  handleAddInspection = () => {
    this.setState({
      visiableInspection: true
    })
  }
  handleCancelClick = () => {
    this.setState({
      visiableInspection: false
    })
  }

  /**
   * 全选和取消全选操作处理
   * */
  cleanSelectedKeys = (flag) => {
    const { newSelectedRowKeys, totalRecords, notSelectedRowKeys } = this.state;
    const {
      systemLog: { systemLogData },
    } = this.props;
    /*判断当前页选中一个点击选择全部情况*/
    let nextSelectedRowKeys =
      newSelectedRowKeys.length == systemLogData.total
        ? newSelectedRowKeys
        : systemLogData.items.map((item) => item.id);
    this.setState({
      singleChoice: flag ? false : true,
      headerShow: !flag ? false : true,
      selectedRowKeys: flag ? nextSelectedRowKeys : [],
      currentSelNum: flag ? totalRecords - notSelectedRowKeys.length : 0,
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const {
      selectedRowKeys,
      currentPage,
      currentPageSize,
      headerShow,
      singleChoice,
      notSelectedRowKeys,
      excelSelRows,
      currentSelNum,
      curPageSelNum,
      loading,
      visiableInspection,
      inspectionList,
    } = this.state;


    //过滤未选中列表
    let curSelectedRowKeys =
      notSelectedRowKeys.length != 0
        ? selectedRowKeys.filter(
          (item) =>
            notSelectedRowKeys.map((item) => item.id).indexOf(item) == -1
        )
        : selectedRowKeys;

    const rowSelection = {
      selectedRowKeys: curSelectedRowKeys,
      onChange: this.onSelectChange,
      onSelectAll: this.onSelectAll,
      onSelect: this.onSelect,
    };

    return (
      <Fragment>
        <Card
          //   title="系统日志"
          style={{
            borderRadius: 8,
          }}
          bordered={false}
        >
          <Form
            className="searchBg"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 14 }}
          >
            <Row>
              <Col span={8}>
                <FormItem label="起止日期">
                  {getFieldDecorator("dateRange")(
                    <RangePicker
                      disabledDate={this.disabledDate}
                      disabledTime={this.disabledRangeTime}
                      showTime
                      locale={locale}
                      format="YYYY-MM-DD HH:mm:ss"
                      onChange={this.onRangChange}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={8} className="searchBtn">
                <Button type="primary" onClick={this.handleSearch}>
                  查询
                </Button>
                <Button style={{ marginLeft: 15 }} onClick={this.handleReset}>
                  清除
                </Button>
              </Col>
            </Row>
          </Form>
        </Card>
        <div style={{ marginBottom: 20, marginTop: 20 }}>
          <Button
            type="primary"
            style={{ marginRight: 8 }}
            onClick={this.handleAddInspection}
          >
            新建巡检
          </Button>
          <Button
            style={{
              borderRadius: 2,
              //   display: this.state.authority == "审计员" ? "none" : "",
            }}
            onClick={this.handleDeleteClick}
          >
            批量删除
          </Button>
        </div>
        <Card
          bordered={false}
          style={{
            borderRadius: 8,
          }}
          className="TableContainer"
        >
          <Table
            rowSelection={rowSelection}
            className={styles.tableHeader}
            dataSource={inspectionList.content}
            loading={loading}
            rowKey={(record) => record.id}
            pagination={false}
            locale={{
              emptyText: "暂无数据信息",
            }}
          >
            <Column
              title="序号 "
              dataIndex="id"
              width={50}
              // fixed="left"
              key="id"
              render= {(text, record, index) => {
                return `${index + 1}`;
              }}
              // render={(text, record) => (
              //   <div style={{ color: primaryColor }}>
              //     <a
              //       style={{
              //         borderRadius: 2,
              //         marginLeft: 10,
              //         color: primaryColor,
              //       }}
              //       onClick={() => {
              //         this.setState({ visiableLook: true , currentRecord:record})
              //       }}
              //     >
              //       {text}
              //     </a>
              //   </div>
              // )}
            />
            <Column
              title="巡检日期"
              dataIndex="inspectionDate"
              key="inspectionDate"
              width={100}
              render={(text, record, index) => {
                return `${moment(record.inspectionDate).format(
                  "YYYY-MM-DD HH:mm:ss"
                )}`;
              }}
            />
            <Column title="手动/自动" 
              dataIndex="inspectionType"
              width={120} 
              key="inspectionType" 
              render={(text, record, index) => {
                return  record.inspectionType==1? '手动':'自动'
              
            }} />
            <Column
              title="巡检设备数"
              dataIndex="totalCounts"
              width={80}
              // fixed="left"
              key="totalCounts"
            />
            <Column
              title="巡检结果"
              dataIndex="inspectionStatus"
              width={120}
              // fixed="left"
              key="inspectionStatus"
              render={(text, record) => {
                return  record.inspectionStatus==0? '未开始': record.inspectionStatus==1?'进行中':'已完成'
              }}
            />
            <Column
              title="巡检状态"
              dataIndex="inspection"
              width={80}
              // fixed="left"
              key="inspection"
              render={(text, record) => (
                <div style={{ color: primaryColor }}>
                  <a       
                    disabled={record.inspectionStatus!=2 ?true: false}
                    onClick={() => {
                        this.downloadExcel(record);
                    }}
                  >
                    导出
                  </a>
                </div>
              )}
            />
            <Column
              title="操作"
              dataIndex="action"
              key="action"
              align='left'
              width={120}
              render={(text, record) => (
                <div style={{ color: primaryColor }}>
                  <a
                    style={{
                      borderRadius: 2,
                      marginLeft: 10,
                      color: primaryColor,
                    }}
                    onClick={() => {
                        this.handleDeleteClick(record);
                    }}
                  >
                    删除
                  </a>
                  <a
                    style={{
                      borderRadius: 2,
                      marginLeft: 10,
                      color: primaryColor,
                    }}
                    onClick={() => {
                        this.handleLookDetail(record);
                    }}
                  >
                    查看
                  </a>
                  <a
                    style={{
                      borderRadius: 2,
                      marginLeft: 10,
                      color: primaryColor,
                    }}
                    onClick={() => {
                        this.handleResult(record);
                    }}
                  >
                    巡检结果
                  </a>
                </div>
              )}
            />
          </Table>
        </Card>
        <Row type="flex" justify="end">
          <Pagination
            current={currentPage}
            showQuickJumper
            showSizeChanger
            onShowSizeChange={this.onShowSizeChange}
            total={inspectionList.totalElements && parseInt(inspectionList.totalElements)}
            showTotal={this.showTotal}
            onChange={this.handlePageChange}
          />
        </Row>
        {visiableInspection && <BatchInspection visiableInspection={visiableInspection} getInspectionData={this.getInspectionData} handleCancelClick={this.handleCancelClick} />}
        {this.state.visiableLook && <InspectionResult visiableLook={this.state.visiableLook} recordData={this.state.recordData} handleCancelView={this.handleCancelView}/>}
        {this.state.visiableDetail && <ViewDetail visiableDetail={this.state.visiableDetail} recordData={this.state.recordData} handleCancelView={this.handleCancelView}/>}
      </Fragment>
    );
  }
}
