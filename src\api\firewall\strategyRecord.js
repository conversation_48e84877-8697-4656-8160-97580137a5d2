import request from '@/util/request'

/**
 * 策略记录-查询接口
 * @param params
 * @returns {Promise}
 */
export function getStrategyRecordList(params) {
  return request({
    url: '/dev/tactics/records',
    method: 'post',
    data: params || {},
  })
}

/**
 * 策略记录-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteStrategyRecord(data) {
  return request({
    url: '/dev/tactics/deleteRecord',
    method: 'post',
    data: data || {},
  })
}
