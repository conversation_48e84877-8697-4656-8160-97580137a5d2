import request from '@util/requestForPy'

/**
 * 哨兵管理-授权管理查询
 * @param params
 * @returns {Promise}
 */
export function getAuthorizationList(params) {
  return request({
    url: '/home_dev/sentinel_license/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 新增授权
 * @param data
 * @returns {Promise}
 */
export function addAuthorization(data) {
  return request({
    url: '/home_dev/sentinel_license/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 更新授权
 * @param data
 * @returns {Promise}
 */
export function updateAuthorization(data) {
  return request({
    url: '/home_dev/sentinel_license/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除授权
 * @param data
 * @returns {Promise}
 */
export function deleteAuthorization(data) {
  return request({
    url: '/home_dev/sentinel_license/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 同步设备授权
 * @param data
 * @returns {Promise}
 */
export function syncDeviceAuth(data) {
  return request({
    url: '/home_dev/sentinel_license/sync',
    method: 'post',
    data: data || {},
  })
}

/**
 * 上传授权文件
 * @param formData
 * @returns {Promise}
 */
export function uploadAuthFile(formData) {
  return request({
    url: '/home_dev/sentinel_license/upload',
    method: 'post',
    data: formData,
  })
}
