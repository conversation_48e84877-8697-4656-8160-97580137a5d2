{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\UpgradeModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\UpgradeModal.vue", "mtime": 1750124511757}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}