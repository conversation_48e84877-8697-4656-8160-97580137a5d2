<template>
  <div class="protocol-set-record">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.name" clearable placeholder="协议集名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.name" clearable placeholder="协议集名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="queryInput.recordTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  @change="handleQuery"
                />
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.operateType" clearable placeholder="操作类型" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="下发" value="0"></el-option>
                  <el-option label="同步" value="1"></el-option>
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.status" clearable placeholder="状态" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="成功" value="1"></el-option>
                  <el-option label="失败" value="0"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="名称"></el-table-column>
          <el-table-column prop="addTime" label="时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.addTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="scope.row.status == '0' ? 'status-failed' : 'status-success'">
                {{ getStatusText(scope.row) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="operateType" label="操作类型">
            <template slot-scope="scope">
              {{ scope.row.operateType == "0" ? "下发" : "同步" }}
            </template>
          </el-table-column>
          <el-table-column prop="counts" label="操作数量"></el-table-column>
          <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'ProtocolSetRecord',
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        name: '',
        recordTime: null,
        operateType: '',
        status: ''
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true
      }
    }
  },
  mounted() {
    this.getRecordList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getRecordList() {
      this.loading = true
      // 这里应该调用协议集记录的API，暂时使用模拟数据
      setTimeout(() => {
        this.tableData = []
        this.pagination.total = 0
        this.loading = false
      }, 500)
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.name) params.name = this.queryInput.name
      if (this.queryInput.operateType !== '') params.operateType = this.queryInput.operateType
      if (this.queryInput.status !== '') params.status = this.queryInput.status
      if (this.queryInput.recordTime && this.queryInput.recordTime.length > 0) {
        params.beginDate = this.queryInput.recordTime[0] + ' 00:00:00'
        params.endDate = this.queryInput.recordTime[1] + ' 23:59:59'
      }
      return params
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getRecordList()
    },
    handleReset() {
      this.queryInput = {
        name: '',
        recordTime: null,
        operateType: '',
        status: ''
      }
      this.handleQuery()
    },
    handleDelete(record) {
      this.$confirm('确定要删除选中记录吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 调用删除API
          this.$message.success('删除成功')
          this.getRecordList()
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm(`确定要删除选中记录吗?删除后不可恢复`, '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 调用批量删除API
          this.$message.success('删除成功')
          this.getRecordList()
        })
        .catch(() => {})
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getRecordList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getRecordList()
    },
    formatTime(time) {
      if (time === '-' || !time) {
        return time
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    getStatusText(record) {
      if (record.status == "0" && record.operateType == "0") {
        return "下发失败"
      } else if (record.status == "1" && record.operateType == "0") {
        return "下发成功"
      } else if (record.status == "0" && record.operateType == "1") {
        return "同步失败"
      } else if (record.status == "1" && record.operateType == "1") {
        return "同步成功"
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.protocol-set-record {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
