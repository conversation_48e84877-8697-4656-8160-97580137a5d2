<template>
  <div class="virus-library">
    <!-- 搜索表单 -->
    <el-card style="border-radius: 8px; margin-bottom: 20px" :bordered="false">
      <el-form :inline="true" :model="searchValue" class="searchBg" label-width="80px">
        <el-form-item label="设备名称:">
          <el-input v-model="searchValue.deviceName" placeholder="设备名称" maxlength="50" clearable></el-input>
        </el-form-item>
        <el-form-item label="升级类型:">
          <el-select v-model="searchValue.status" placeholder="全部">
            <el-option label="全部" :value="-1"></el-option>
            <el-option label="升级成功" :value="0"></el-option>
            <el-option label="升级失败" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" :loading="loading" @click="handleAdvancedSearch">查询</el-button>
        <el-button style="margin-left: 15px" @click="handleReset">清除</el-button>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div style="margin-bottom: 20px">
      <el-button type="primary" style="margin-right: 8px" @click="handleAdd">新建更新</el-button>
    </div>

    <!-- 数据表格 -->
    <el-card :bordered="false" style="border-radius: 8px" class="TableContainer">
      <el-table
        :data="tableList"
        v-loading="loading"
        row-key="id"
        :pagination="false"
        element-loading-text="暂无数据信息"
        :scroll="{ x: 1500 }"
        size="mini"
      >
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称"></el-table-column>
        <el-table-column prop="deviceIp" label="设备IP"></el-table-column>
        <el-table-column prop="groupName" label="设备组"></el-table-column>
        <el-table-column prop="status" label="病毒库更新状态">
          <template slot-scope="scope">
            <span :class="scope.row.status == 1 ? 'redColor' : 'blueColor'">
              {{ scope.row.status == 0 ? '成功' : '失败' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="最后一次更新时间">
          <template slot-scope="scope">
            {{ scope.row.updateTime ? formatDate(scope.row.updateTime) : '' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left">
          <template slot-scope="scope">
            <div style="color: #1890ff">
              <el-button type="text" style="border-radius: 2px; margin-left: 10px; color: #1890ff" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <el-row type="flex" justify="end" style="margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-size="currentPageSize"
        :total="tableData.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onShowSizeChange"
        @current-change="handlePageChange"
        :show-total="showTotal"
      ></el-pagination>
    </el-row>

    <!-- 新建更新对话框 -->
    <add-sentine-update-modal
      :visiable-add-update="visiableAddUpdate"
      :type="'1'"
      @handle-add-cancel="handleAddCancel"
      @get-update-list="getUpdateList"
    ></add-sentine-update-modal>
  </div>
</template>

<script>
import AddSentineUpdateModal from './AddSentineUpdateModal.vue'
import { getVirusLibraryList, deleteVirusLibrary } from '@/api/sentine/upgradeManagement'
import { calcPageNo } from '@/util/common'
import moment from 'moment'

export default {
  name: 'VirusSentineLibrary',
  components: {
    AddSentineUpdateModal,
  },
  data() {
    return {
      currentPage: 1,
      currentPageSize: 10,
      selectedRowKeys: [],
      searchValue: {
        deviceName: '',
        status: -1,
      },
      totalRecords: 0,
      activeKey: '1',
      visiableAddUpdate: false,
      tableList: [],
      tableData: {},
      loading: false,
    }
  },
  mounted() {
    this.getUpdateList()
  },
  methods: {
    async getUpdateList() {
      this.loading = true

      try {
        const res = await getVirusLibraryList({
          pageSize: this.currentPageSize,
          pageIndex: this.currentPage,
          status: this.searchValue.status === -1 ? undefined : this.searchValue.status,
          name: this.searchValue.deviceName,
          type: 1, // "0 版本升级  1 病毒库 2 入侵规则库"
        })

        if (res.retcode === 0) {
          this.tableList = res.data.rows || []
          this.selectedRowKeys = []
          this.tableData = res.data
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
    },

    handleDeleteClick() {
      // 删除逻辑
    },

    handleAdd() {
      this.visiableAddUpdate = true
    },

    handleAddCancel() {
      this.visiableAddUpdate = false
    },

    handlePageChange(pageNumber) {
      this.currentPage = pageNumber
      this.getUpdateList()
    },

    onShowSizeChange(current, pageSize) {
      this.currentPage = current
      this.currentPageSize = pageSize
      this.getUpdateList()
    },

    showTotal() {
      return `总数据${this.tableData.total || 0}条`
    },

    handleAdvancedSearch() {
      // 验证搜索条件
      let searchValue = {}
      if (this.searchValue.deviceName) searchValue.deviceName = this.searchValue.deviceName
      if (this.searchValue.status !== undefined && this.searchValue.status !== -1) {
        searchValue.status = this.searchValue.status
      }

      this.searchValue = searchValue
      this.currentPage = 1
      this.getUpdateList()
    },

    handleReset() {
      this.searchValue = {
        deviceName: '',
        status: -1,
      }
    },

    async handleDelete(record) {
      this.$confirm('确定删除选中的数据吗?删除后不可恢复？', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteVirusLibrary({ ids: record.id })
            if (res.retcode === 0) {
              this.currentPage = calcPageNo(this.tableData, 1)
              this.getUpdateList()
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },

    formatDate(dateString) {
      return dateString ? moment(dateString).format('YYYY-MM-DD HH:mm:ss') : ''
    },
  },
}
</script>

<style lang="scss" scoped>
.virus-library {
  .searchBg {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .searchBtn {
    text-align: right;
  }

  .TableContainer {
    .redColor {
      color: #f5222d;
    }

    .blueColor {
      color: #1890ff;
    }
  }
}
</style>
