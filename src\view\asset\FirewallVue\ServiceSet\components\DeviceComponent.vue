<template>
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="选择设备" prop="deviceIds">
          <el-tree
            ref="deviceTree"
            :data="deviceData"
            show-checkbox
            node-key="value"
            :props="treeProps"
            :check-strictly="false"
            @check="handleTreeCheck"
            :default-checked-keys="selectedDeviceIds"
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getDeviceList, issueServiceSet, syncServiceSetFromDevice } from '@/api/firewall/serviceSet'

export default {
  name: 'DeviceComponent',
  props: {
    operationType: {
      type: String,
      default: '1', // '1': 下发, '2': 同步
    },
  },
  data() {
    return {
      drawerVisible: false,
      loading: false,
      title: '下发服务',
      formData: {
        deviceIds: [],
      },
      rules: {
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      currentRecord: {},
      currentIds: [],
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0',
      },
    }
  },
  watch: {
    operationType: {
      handler(val) {
        this.title = val === '1' ? '下发服务' : '同步设备服务'
      },
      immediate: true,
    },
  },
  methods: {
    async showDrawer(record = {}, ids = []) {
      this.currentRecord = record
      this.currentIds = ids
      this.selectedDeviceIds = [...ids]
      this.drawerVisible = true
      
      await this.loadDeviceData()
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({ id: this.currentRecord.id })
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          value: `${item.type},${item.compId},${item.srcId}`,
          disabled: item.type === '0',
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.value && node.value.startsWith('1,')) {
          const parts = node.value.split(',')
          if (parts.length >= 3) {
            deviceIds.push(parts[2])
          }
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const confirmMessage = this.operationType === '1' 
            ? '下发服务后不可修改，是否确认下发？' 
            : '同步设备服务后不可修改，是否确认同步设备服务？'
            
          this.$confirm(confirmMessage, '确认操作', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            this.loading = true
            try {
              let res
              const params = {
                bandDeviceIds: this.formData.deviceIds.join(','),
                ids: this.currentIds.join(','),
              }
              
              if (this.operationType === '1') {
                res = await issueServiceSet(params)
              } else {
                res = await syncServiceSetFromDevice(params)
              }
              
              if (res.retcode === 0) {
                this.$message.success(res.msg || '操作成功')
                this.$emit('on-submit')
                this.handleClose()
              } else {
                this.$message.error(res.msg)
              }
            } catch (error) {
              this.$message.error('操作失败')
            } finally {
              this.loading = false
            }
          }).catch(() => {})
        }
      })
    },
    handleClose() {
      this.drawerVisible = false
      this.formData = {
        deviceIds: [],
      }
      this.selectedDeviceIds = []
      this.currentRecord = {}
      this.currentIds = []
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin: 0 10px;
}

:deep(.el-tree) {
  max-height: 400px;
  overflow: auto;
}
</style>
