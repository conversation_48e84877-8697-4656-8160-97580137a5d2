<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="巡检名称" prop="inspectionName">
        <el-input v-model="formData.inspectionName" placeholder="请输入巡检名称" />
      </el-form-item>

      <el-form-item label="选择设备" prop="deviceIds">
        <el-tree
          ref="deviceTree"
          :data="deviceData"
          show-checkbox
          node-key="srcId"
          :props="treeProps"
          :check-strictly="false"
          @check="handleTreeCheck"
          :default-checked-keys="selectedDeviceIds"
          style="max-height: 200px; overflow: auto;"
        />
      </el-form-item>

      <el-form-item label="巡检类型" prop="inspectionType">
        <el-checkbox-group v-model="formData.inspectionType">
          <el-checkbox value="connectivity">连通性检查</el-checkbox>
          <el-checkbox value="performance">性能检查</el-checkbox>
          <el-checkbox value="security">安全检查</el-checkbox>
          <el-checkbox value="config">配置检查</el-checkbox>
          <el-checkbox value="log">日志检查</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="执行方式" prop="executeType">
        <el-radio-group v-model="formData.executeType" @change="handleExecuteTypeChange">
          <el-radio value="immediate">立即执行</el-radio>
          <el-radio value="scheduled">定时执行</el-radio>
          <el-radio value="periodic">周期执行</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="执行时间" prop="executeTime" v-if="formData.executeType === 'scheduled'">
        <el-date-picker
          v-model="formData.executeTime"
          type="datetime"
          placeholder="选择执行时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="执行周期" prop="executeCycle" v-if="formData.executeType === 'periodic'">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-select v-model="formData.executeCycle.type" placeholder="周期类型">
              <el-option label="每天" value="daily"></el-option>
              <el-option label="每周" value="weekly"></el-option>
              <el-option label="每月" value="monthly"></el-option>
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-time-picker
              v-model="formData.executeCycle.time"
              placeholder="执行时间"
              format="HH:mm"
              value-format="HH:mm"
            />
          </el-col>
          <el-col :span="8" v-if="formData.executeCycle.type === 'weekly'">
            <el-select v-model="formData.executeCycle.weekday" placeholder="星期">
              <el-option label="星期一" value="1"></el-option>
              <el-option label="星期二" value="2"></el-option>
              <el-option label="星期三" value="3"></el-option>
              <el-option label="星期四" value="4"></el-option>
              <el-option label="星期五" value="5"></el-option>
              <el-option label="星期六" value="6"></el-option>
              <el-option label="星期日" value="0"></el-option>
            </el-select>
          </el-col>
          <el-col :span="8" v-if="formData.executeCycle.type === 'monthly'">
            <el-input-number
              v-model="formData.executeCycle.day"
              :min="1"
              :max="31"
              placeholder="日期"
            />
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入备注"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addInspection, updateInspection, getDeviceList } from '@/api/firewall/inspectionManagement'

export default {
  name: 'AddInspectionModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      title: '新增巡检任务',
      formData: {
        id: null,
        inspectionName: '',
        deviceIds: [],
        inspectionType: ['connectivity'],
        executeType: 'immediate',
        executeTime: '',
        executeCycle: {
          type: 'daily',
          time: '',
          weekday: '1',
          day: 1,
        },
        remark: '',
      },
      rules: {
        inspectionName: [
          { required: true, message: '请输入巡检名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5\w]{1,30}$/, message: '字符串长度范围: 1 - 30', trigger: 'blur' },
        ],
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
        inspectionType: [
          { required: true, message: '请选择巡检类型', trigger: 'change' },
        ],
        executeType: [
          { required: true, message: '请选择执行方式', trigger: 'change' },
        ],
        executeTime: [
          { required: true, message: '请选择执行时间', trigger: 'change' },
        ],
        executeCycle: [
          { required: true, message: '请设置执行周期', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    isEdit() {
      return this.currentData && this.currentData.id
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadDeviceData()
      }
    },
  },
  methods: {
    initForm() {
      if (this.isEdit) {
        this.title = '编辑巡检任务'
        this.formData = {
          id: this.currentData.id,
          inspectionName: this.currentData.inspectionName || '',
          deviceIds: this.currentData.deviceIds ? this.currentData.deviceIds.split(',') : [],
          inspectionType: this.currentData.inspectionType ? this.currentData.inspectionType.split(',') : ['connectivity'],
          executeType: this.currentData.executeType || 'immediate',
          executeTime: this.currentData.executeTime || '',
          executeCycle: this.currentData.executeCycle || {
            type: 'daily',
            time: '',
            weekday: '1',
            day: 1,
          },
          remark: this.currentData.remark || '',
        }
      } else {
        this.title = '新增巡检任务'
        this.formData = {
          id: null,
          inspectionName: '',
          deviceIds: [],
          inspectionType: ['connectivity'],
          executeType: 'immediate',
          executeTime: '',
          executeCycle: {
            type: 'daily',
            time: '',
            weekday: '1',
            day: 1,
          },
          remark: '',
        }
      }
      this.selectedDeviceIds = this.formData.deviceIds
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      // 提取设备ID（type为'1'的节点）
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.type === '1') {
          deviceIds.push(node.srcId)
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    handleExecuteTypeChange(value) {
      // 清空相关字段
      this.formData.executeTime = ''
      this.formData.executeCycle = {
        type: 'daily',
        time: '',
        weekday: '1',
        day: 1,
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const submitData = {
              ...this.formData,
              deviceIds: this.formData.deviceIds.join(','),
              inspectionType: this.formData.inspectionType.join(','),
              executeCycle: JSON.stringify(this.formData.executeCycle),
            }
            
            let res
            if (this.isEdit) {
              res = await updateInspection(submitData)
            } else {
              res = await addInspection(submitData)
            }
            
            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
