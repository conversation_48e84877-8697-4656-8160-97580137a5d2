import React, { Component, Fragment } from 'react';
import { Modal, Form, Input, Radio, Select, Button, message, Card, Row, Col } from 'antd';
import { connect } from 'dva';
import IPut from '@/components/IPInput/IPInput';
import styles from '../index.less'
const FormItem = Form.Item;
const Option = Select.Option;
const RadioGroup = Radio.Group;

@Form.create()
@connect(({ fireWall }) => ({
	fireWall
}), null, null, { withRef: true })
export default class AddAccountModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			confirmDirty: false,
			value: 1,
		}
	}

	//查询
	handleAdvancedSearch = () => {
		const { form, dispatch, id } = this.props;
		form.validateFields((err, values) => {
			let searchValue = {};
			if (values.fireName) searchValue.fireName = values.fireName;
			if (values.ip) searchValue.originIp = values.ip;
			if (values.onlinStatus) searchValue.onlinStatus = values.onlinStatus;
			this.setState({
				searchValue: searchValue
			},
				() => {
					this.props.getInitStates()
				}
			)

		})
	}
	// handleAdvancedSearch = () => {
	//     const { form } = this.props;
	// form.validateFields((err, values) => {
	//     if (!err) {
	//         let searchValue = {};
	//         if (values.fireName) searchValue.fireName = values.fireName;
	//         if (values.ip) searchValue.originIp = values.ip;
	//         if (values.onlinStatus) searchValue.onlinStatus = values.onlinStatus;
	//         this.setState({
	//             searchValue: searchValue
	//         },
	//             () => {
	// 				this.props.getInitStates()
	//             }
	//         )
	//     }
	// })
	// }

	render() {
		const { visible } = this.state;
		const { getFieldDecorator } = this.props.form;
		return (
			<Fragment>
				<Card title="设备列表" bordered={false}>
					<Form>
						<Row>
							<Col span={6}>
								<FormItem
									labelCol={{ span: 6 }}
									wrapperCol={{ span: 14 }}
									label="名称:"
								>
									{getFieldDecorator('fireName', {
										rules: [{
											pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,
											message: '请输入汉字、字母、数字、短横线或下划线',
										}]
									})(
										<Input maxLength={50} placeholder='产品名称' />
									)}
								</FormItem>
							</Col>
							<Col span={6}>
								<FormItem
									labelCol={{ span: 6 }}
									wrapperCol={{ span: 14 }}
									label="IP:"
								>
									{getFieldDecorator('ip', {
										rules: [{
											pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_\.]*$/,
											message: '请输入汉字、字母、数字、短横线、点或下划线',
										}]
									})(
										<Input maxLength={50} placeholder='IP' />
									)}
								</FormItem>
							</Col>
							<Col span={6}>
								<FormItem
									labelCol={{ span: 6 }}
									wrapperCol={{ span: 14 }}
									label="在线状态:"
								>
									{getFieldDecorator('onlinStatus', { initialValue: '' })(
										<Select className={styles.contentWidth}>
											<Option value="">全部</Option>
											<Option value="0">在线</Option>
											<Option value="1">离线</Option>
										</Select>
									)}
								</FormItem>
							</Col>
							<Col span={2} style={{ float: 'right' }}>
								<Button type="primary" onClick={this.handleOkRemark}>查询</Button>
							</Col>
						</Row>
					</Form>
				</Card>
			</Fragment >
		)
	}
}