<template>
  <el-dialog
    title="创建备份"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="备份名称" prop="backupName">
        <el-input v-model="formData.backupName" placeholder="请输入备份名称" />
      </el-form-item>

      <el-form-item label="选择设备" prop="deviceIds">
        <el-tree
          ref="deviceTree"
          :data="deviceData"
          show-checkbox
          node-key="srcId"
          :props="treeProps"
          :check-strictly="false"
          @check="handleTreeCheck"
          :default-checked-keys="selectedDeviceIds"
        />
      </el-form-item>

      <el-form-item label="备份类型" prop="backupType">
        <el-radio-group v-model="formData.backupType">
          <el-radio value="full">完整备份</el-radio>
          <el-radio value="config">配置备份</el-radio>
          <el-radio value="policy">策略备份</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入备注"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">创建备份</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createBackup, getDeviceList } from '@/api/firewall/backupRestore'

export default {
  name: 'BackupModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      formData: {
        backupName: '',
        deviceIds: [],
        backupType: 'full',
        remark: '',
      },
      rules: {
        backupName: [
          { required: true, message: '请输入备份名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5\w]{1,30}$/, message: '字符串长度范围: 1 - 30', trigger: 'blur' },
        ],
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' },
        ],
        backupType: [
          { required: true, message: '请选择备份类型', trigger: 'change' },
        ],
      },
      deviceData: [],
      selectedDeviceIds: [],
      treeProps: {
        children: 'childList',
        label: 'name',
        disabled: (data) => data.type === '0', // 分组节点禁用
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadDeviceData()
      }
    },
  },
  methods: {
    initForm() {
      this.formData = {
        backupName: '',
        deviceIds: [],
        backupType: 'full',
        remark: '',
      }
      this.selectedDeviceIds = []
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async loadDeviceData() {
      this.loading = true
      try {
        const res = await getDeviceList({})
        if (res.retcode === 0) {
          this.deviceData = this.transformTreeData(res.data || [])
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    transformTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          disabled: item.type === '0', // 分组节点禁用选择
        }
        
        if (item.childList && item.childList.length > 0) {
          node.childList = this.transformTreeData(item.childList)
        }
        
        return node
      })
    },
    handleTreeCheck(checkedNodes, checkedInfo) {
      // 提取设备ID（type为'1'的节点）
      const deviceIds = []
      const allCheckedNodes = checkedInfo.checkedNodes || []
      
      allCheckedNodes.forEach(node => {
        if (node.type === '1') {
          deviceIds.push(node.srcId)
        }
      })
      
      this.formData.deviceIds = deviceIds
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$confirm('确认创建备份吗？备份过程可能需要一些时间。', '确认备份', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            this.submitLoading = true
            try {
              const params = {
                backupName: this.formData.backupName,
                deviceIds: this.formData.deviceIds.join(','),
                backupType: this.formData.backupType,
                remark: this.formData.remark,
              }
              
              const res = await createBackup(params)
              if (res.retcode === 0) {
                this.$message.success('备份任务已创建，请稍后查看备份结果')
                this.$emit('on-submit')
                this.handleClose()
              } else {
                this.$message.error(res.msg)
              }
            } catch (error) {
              this.$message.error('创建备份失败')
            } finally {
              this.submitLoading = false
            }
          }).catch(() => {})
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

:deep(.el-tree) {
  max-height: 300px;
  overflow: auto;
}
</style>
