export default [

  {
    path: '/guard/device-list',
    name: '<PERSON>ceList',
    component: () => import('@/view/asset/SentineVue/DeviceManagement/index'),
    meta: { title: '设备列表', icon: 'list' }
  },
  {
    path: '/guard/device-group',
    name: 'DeviceGroup',
    component: () => import('@/view/asset/Hostguardiangroup/GroupManagement'),
    meta: { title: '设备分组', icon: 'group' }
  },
  {
    path: '/guard/upgrade-management',
    name: 'UpgradeManagement',
    component: () => import('@/view/asset/SentineVue/UpgradeManagement/index'),
    meta: { title: '升级管理', icon: 'upgrade' }
  },
  {
    path: '/guard/auth-management',
    name: 'AuthManagement',
    component: () => import('@/view/asset/SentineVue/AuthManagement/index'),
    meta: { title: '认证管理', icon: 'auth' }
  },
  {
    path: '/guard/strategy/manage',
    name: 'StrategyManagement',
    component: () => import('@/view/asset/SentineVue/StrategySentine/index'),
    meta: { title: '策略管理', icon: 'strategy' }
  },
  {
    path: '/guard/strategy-record',
    name: 'StrategyRecord',
    component: () => import('@/view/asset/SentineVue/StrategySentine/index'),
    meta: { title: '策略记录', icon: 'record' }
  }
]
