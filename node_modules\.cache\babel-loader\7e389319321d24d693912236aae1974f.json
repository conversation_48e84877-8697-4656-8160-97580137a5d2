{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\StrategySentine\\components\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\StrategySentine\\components\\DeviceComponent.vue", "mtime": 1750058915555}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}