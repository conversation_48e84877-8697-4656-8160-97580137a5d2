<template>
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>

        <el-form-item label="协议类型" prop="protocolType">
          <el-select v-model="formData.protocolType" placeholder="请选择协议类型" @change="handleProtocolChange">
            <el-option label="Modbus" value="Modbus"></el-option>
            <el-option label="DNP3" value="DNP3"></el-option>
            <el-option label="IEC104" value="IEC104"></el-option>
            <el-option label="OPC" value="OPC"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="功能码" prop="functionCode" v-if="showFunctionCode">
          <el-select v-model="formData.functionCode" placeholder="请选择功能码" multiple>
            <el-option
              v-for="item in functionCodeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="端口范围" prop="portRange">
          <el-row :gutter="10">
            <el-col :span="11">
              <el-input v-model="formData.startPort" placeholder="起始端口" />
            </el-col>
            <el-col :span="2" style="text-align: center">-</el-col>
            <el-col :span="11">
              <el-input v-model="formData.endPort" placeholder="结束端口" />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
            :rows="3"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addProtocolSet, updateProtocolSet, getProtocolSetInfo, getFunctionCodeList } from '@/api/firewall/protocolSet'

export default {
  name: 'AddProtocolModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      title: '新增工控协议集',
      formData: {
        id: null,
        name: '',
        protocolType: '',
        functionCode: [],
        startPort: '',
        endPort: '',
        remark: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5\w]{1,20}$/, message: '字符串长度范围: 1 - 20', trigger: 'blur' },
        ],
        protocolType: [
          { required: true, message: '请选择协议类型', trigger: 'change' },
        ],
        startPort: [
          { required: true, message: '请输入起始端口', trigger: 'blur' },
          { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: '端口范围: 1-65535', trigger: 'blur' },
        ],
        endPort: [
          { required: true, message: '请输入结束端口', trigger: 'blur' },
          { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: '端口范围: 1-65535', trigger: 'blur' },
        ],
        remark: [
          { max: 30, message: '备注长度不超过30字', trigger: 'blur' },
        ],
      },
      functionCodeOptions: [],
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    showFunctionCode() {
      return ['Modbus', 'DNP3'].includes(this.formData.protocolType)
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
  },
  methods: {
    async initForm() {
      if (this.currentData && this.currentData.id) {
        this.title = '编辑工控协议集'
        this.loading = true
        try {
          const res = await getProtocolSetInfo({ id: this.currentData.id })
          if (res.retcode === 0) {
            this.formData = {
              id: res.data.id,
              name: res.data.name,
              protocolType: res.data.protocolType,
              functionCode: res.data.functionCode ? res.data.functionCode.split(',') : [],
              startPort: res.data.startPort,
              endPort: res.data.endPort,
              remark: res.data.remark,
            }
            if (this.showFunctionCode) {
              await this.loadFunctionCodes()
            }
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          this.$message.error('获取协议集信息失败')
        } finally {
          this.loading = false
        }
      } else {
        this.title = '新增工控协议集'
        this.formData = {
          id: null,
          name: '',
          protocolType: '',
          functionCode: [],
          startPort: '',
          endPort: '',
          remark: '',
        }
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    async handleProtocolChange(value) {
      this.formData.functionCode = []
      if (this.showFunctionCode) {
        await this.loadFunctionCodes()
      }
    },
    async loadFunctionCodes() {
      try {
        const res = await getFunctionCodeList({ protocolType: this.formData.protocolType })
        if (res.retcode === 0) {
          this.functionCodeOptions = res.data || []
        }
      } catch (error) {
        console.error('获取功能码失败:', error)
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 验证端口范围
          const startPort = parseInt(this.formData.startPort)
          const endPort = parseInt(this.formData.endPort)
          if (startPort > endPort) {
            this.$message.error('起始端口不能大于结束端口')
            return
          }

          this.loading = true
          try {
            const submitData = {
              ...this.formData,
              functionCode: this.formData.functionCode.join(','),
            }
            
            let res
            if (this.title.includes('新增')) {
              res = await addProtocolSet(submitData)
            } else {
              res = await updateProtocolSet(submitData)
            }
            
            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    handleClose() {
      this.drawerVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin: 0 10px;
}
</style>
