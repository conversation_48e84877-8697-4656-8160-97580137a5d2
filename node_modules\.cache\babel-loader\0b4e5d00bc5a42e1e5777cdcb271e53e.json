{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue", "mtime": 1750063899296}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0IjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUiOwppbXBvcnQgInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSI7CmltcG9ydCBfYXN5bmNUb0dlbmVyYXRvciBmcm9tICJEOi93b3Jrc3BhY2Uvc21wL25vZGVfbW9kdWxlcy9AdnVlL2JhYmVsLXByZXNldC1hcHAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwppbXBvcnQgeyBnZXRGaXJld2FsbERldmljZUxpc3QgYXMgX2dldEZpcmV3YWxsRGV2aWNlTGlzdCwgZGVsZXRlRmlyZXdhbGxEZXZpY2UsIGJhdGNoRGVsZXRlRmlyZXdhbGxEZXZpY2UsIGRldmljZVBpbmcgfSBmcm9tICdAL2FwaS9maXJld2FsbC9kZXZpY2VNYW5hZ2VtZW50JzsKaW1wb3J0IEFkZERldmljZU1vZGFsIGZyb20gJy4vY29tcG9uZW50cy9BZGREZXZpY2VNb2RhbC52dWUnOwppbXBvcnQgVXNlck1hbmFnZU1vZGFsIGZyb20gJy4vY29tcG9uZW50cy9Vc2VyTWFuYWdlTW9kYWwudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEZXZpY2VMaXN0JywKICBjb21wb25lbnRzOiB7CiAgICBBZGREZXZpY2VNb2RhbDogQWRkRGV2aWNlTW9kYWwsCiAgICBVc2VyTWFuYWdlTW9kYWw6IFVzZXJNYW5hZ2VNb2RhbAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZVRhYjogJzAnLAogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcXVlcnlJbnB1dDogewogICAgICAgIGZpcmVOYW1lOiAnJywKICAgICAgICBvcmlnaW5JcDogJycsCiAgICAgICAgb25saW5TdGF0dXM6ICcnCiAgICAgIH0sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIHNlbGVjdGVkUm93czogW10sCiAgICAgIHBhZ2luYXRpb246IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LAogICAgICBhZGRNb2RhbFZpc2libGU6IGZhbHNlLAogICAgICB1c2VyTW9kYWxWaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudERhdGE6IG51bGwsCiAgICAgIHBhbmVzOiBbXSwKICAgICAgLy8g5Yqo5oCB5qCH562+6aG1CiAgICAgIHRpbWVyOiBudWxsIC8vIOWumuaXtuWZqAoKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKTsKICAgIHRoaXMuc3RhcnRUaW1lcigpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuY2xlYXJUaW1lcigpOwogIH0sCiAgbWV0aG9kczogewogICAgdG9nZ2xlU2hvdzogZnVuY3Rpb24gdG9nZ2xlU2hvdygpIHsKICAgICAgdGhpcy5pc1Nob3cgPSAhdGhpcy5pc1Nob3c7CiAgICB9LAogICAgc3RhcnRUaW1lcjogZnVuY3Rpb24gc3RhcnRUaW1lcigpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKCiAgICAgIC8vIOavjzMw56eS5Yi35paw5LiA5qyh6K6+5aSH5YiX6KGoCiAgICAgIHRoaXMudGltZXIgPSBzZXRJbnRlcnZhbChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7CiAgICAgIH0sIDMwMDAwKTsKICAgIH0sCiAgICBjbGVhclRpbWVyOiBmdW5jdGlvbiBjbGVhclRpbWVyKCkgewogICAgICBpZiAodGhpcy50aW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lcik7CiAgICAgICAgdGhpcy50aW1lciA9IG51bGw7CiAgICAgIH0KICAgIH0sCiAgICBnZXRGaXJld2FsbERldmljZUxpc3Q6IGZ1bmN0aW9uIGdldEZpcmV3YWxsRGV2aWNlTGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBwYXlsb2FkLCByZXM7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgICAgcGF5bG9hZCA9IHsKICAgICAgICAgICAgICAgICAgX2xpbWl0OiBfdGhpczIucGFnaW5hdGlvbi5wYWdlU2l6ZSwKICAgICAgICAgICAgICAgICAgX3BhZ2U6IF90aGlzMi5wYWdpbmF0aW9uLmN1cnJlbnRQYWdlLAogICAgICAgICAgICAgICAgICBxdWVyeVBhcmFtczogX3RoaXMyLmJ1aWxkUXVlcnlQYXJhbXMoKSwKICAgICAgICAgICAgICAgICAgdHlwZTogMQogICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAyOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDU7CiAgICAgICAgICAgICAgICByZXR1cm4gX2dldEZpcmV3YWxsRGV2aWNlTGlzdChwYXlsb2FkKTsKCiAgICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLnRhYmxlRGF0YSA9IHJlcy5kYXRhLml0ZW1zIHx8IFtdOwogICAgICAgICAgICAgICAgICBfdGhpczIucGFnaW5hdGlvbi50b3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7CiAgICAgICAgICAgICAgICAgIF90aGlzMi5zZWxlY3RlZFJvd3MgPSBbXTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEyOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSA5OwogICAgICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSgyKTsKCiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluiuvuWkh+WIl+ihqOWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDEyOwogICAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5maW5pc2goMTIpOwoKICAgICAgICAgICAgICBjYXNlIDE1OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSwgbnVsbCwgW1syLCA5LCAxMiwgMTVdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGJ1aWxkUXVlcnlQYXJhbXM6IGZ1bmN0aW9uIGJ1aWxkUXVlcnlQYXJhbXMoKSB7CiAgICAgIHZhciBwYXJhbXMgPSB7fTsKICAgICAgaWYgKHRoaXMucXVlcnlJbnB1dC5maXJlTmFtZSkgcGFyYW1zLmZpcmVOYW1lID0gdGhpcy5xdWVyeUlucHV0LmZpcmVOYW1lOwogICAgICBpZiAodGhpcy5xdWVyeUlucHV0Lm9yaWdpbklwKSBwYXJhbXMub3JpZ2luSXAgPSB0aGlzLnF1ZXJ5SW5wdXQub3JpZ2luSXA7CiAgICAgIGlmICh0aGlzLnF1ZXJ5SW5wdXQub25saW5TdGF0dXMgIT09ICcnKSBwYXJhbXMub25saW5TdGF0dXMgPSB0aGlzLnF1ZXJ5SW5wdXQub25saW5TdGF0dXM7CiAgICAgIHJldHVybiBwYXJhbXM7CiAgICB9LAogICAgaGFuZGxlVGFiQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZVRhYkNsaWNrKHRhYikgewogICAgICB0aGlzLmFjdGl2ZVRhYiA9IHRhYi5uYW1lOwoKICAgICAgaWYgKHRhYi5uYW1lICE9PSAnMCcpIHsvLyDlpITnkIbliqjmgIHmoIfnrb7pobXngrnlh7sKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5wYWdpbmF0aW9uLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKTsKICAgIH0sCiAgICBoYW5kbGVSZXNldDogZnVuY3Rpb24gaGFuZGxlUmVzZXQoKSB7CiAgICAgIHRoaXMucXVlcnlJbnB1dCA9IHsKICAgICAgICBmaXJlTmFtZTogJycsCiAgICAgICAgb3JpZ2luSXA6ICcnLAogICAgICAgIG9ubGluU3RhdHVzOiAnJwogICAgICB9OwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMuY3VycmVudERhdGEgPSBudWxsOwogICAgICB0aGlzLmFkZE1vZGFsVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlRWRpdDogZnVuY3Rpb24gaGFuZGxlRWRpdChyZWNvcmQpIHsKICAgICAgdGhpcy5jdXJyZW50RGF0YSA9IHJlY29yZDsKICAgICAgdGhpcy5hZGRNb2RhbFZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZVZpZXc6IGZ1bmN0aW9uIGhhbmRsZVZpZXcocmVjb3JkKSB7CiAgICAgIC8vIOaJk+W8gOiuvuWkh+euoeeQhumhtemdogogICAgICB2YXIgcGFuZSA9IHsKICAgICAgICB0aXRsZTogcmVjb3JkLmZpcmVOYW1lLAogICAgICAgIG5hbWU6IHJlY29yZC5pcCwKICAgICAgICBjb250ZW50OiAiaHR0cHM6Ly8iLmNvbmNhdChyZWNvcmQub3JpZ2luSXAsICI6IikuY29uY2F0KHJlY29yZC5wb3J0KQogICAgICB9OyAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/miZPlvIAKCiAgICAgIHZhciBleGlzdGluZ1BhbmUgPSB0aGlzLnBhbmVzLmZpbmQoZnVuY3Rpb24gKHApIHsKICAgICAgICByZXR1cm4gcC5uYW1lID09PSBwYW5lLm5hbWU7CiAgICAgIH0pOwoKICAgICAgaWYgKCFleGlzdGluZ1BhbmUpIHsKICAgICAgICB0aGlzLnBhbmVzLnB1c2gocGFuZSk7CiAgICAgIH0KCiAgICAgIHRoaXMuYWN0aXZlVGFiID0gcGFuZS5uYW1lOwogICAgfSwKICAgIGhhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJlY29yZCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOivpeiuvuWkh+WQlz/liKDpmaTlkI7kuI3lj6/mgaLlpI0nLCAn5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu6K6kJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbiggLyojX19QVVJFX18qL19hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHJlczsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAwOwogICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAzOwogICAgICAgICAgICAgICAgcmV0dXJuIGRlbGV0ZUZpcmV3YWxsRGV2aWNlKHsKICAgICAgICAgICAgICAgICAgZGV2aWNlX2lkOiByZWNvcmQuaWQKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDIuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwoKICAgICAgICAgICAgICAgICAgX3RoaXMzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSA3OwogICAgICAgICAgICAgICAgX2NvbnRleHQyLnQwID0gX2NvbnRleHQyWyJjYXRjaCJdKDApOwoKICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIsIG51bGwsIFtbMCwgN11dKTsKICAgICAgfSkpKS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgaGFuZGxlQmF0Y2hEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZUJhdGNoRGVsZXRlKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfoh7PlsJHpgInkuK3kuIDmnaHmlbDmja4nKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOmAieS4reiuvuWkh+WQlz/liKDpmaTlkI7kuI3lj6/mgaLlpI0nLCAn5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu6K6kJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbiggLyojX19QVVJFX18qL19hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgICAgdmFyIGlkcywgcmVzOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDMucHJldiA9IDA7CiAgICAgICAgICAgICAgICBpZHMgPSBfdGhpczQuc2VsZWN0ZWRSb3dzLm1hcChmdW5jdGlvbiAocm93KSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiByb3cuaWQ7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gNDsKICAgICAgICAgICAgICAgIHJldHVybiBiYXRjaERlbGV0ZUZpcmV3YWxsRGV2aWNlKHsKICAgICAgICAgICAgICAgICAgZGV2aWNlX2lkczogaWRzCiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQzLnNlbnQ7CgogICAgICAgICAgICAgICAgaWYgKHJlcy5yZXRjb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKCiAgICAgICAgICAgICAgICAgIF90aGlzNC5nZXRGaXJld2FsbERldmljZUxpc3QoKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDExOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gODsKICAgICAgICAgICAgICAgIF9jb250ZXh0My50MCA9IF9jb250ZXh0M1siY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzLCBudWxsLCBbWzAsIDhdXSk7CiAgICAgIH0pKSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGhhbmRsZVBpbmc6IGZ1bmN0aW9uIGhhbmRsZVBpbmcocmVjb3JkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIHJlczsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDQucHJldiA9IF9jb250ZXh0NC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQ0LnByZXYgPSAwOwoKICAgICAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5pbmZvKCfmraPlnKjmtYvor5Xov57mjqUuLi4nKTsKCiAgICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDQ7CiAgICAgICAgICAgICAgICByZXR1cm4gZGV2aWNlUGluZyh7CiAgICAgICAgICAgICAgICAgIGlwOiByZWNvcmQub3JpZ2luSXAKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDQuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+iuvuWkh+i/nuaOpeato+W4uCcpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmVycm9yKCforr7lpIfov57mjqXlpLHotKUnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDExOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5wcmV2ID0gODsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC50MCA9IF9jb250ZXh0NFsiY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczUuJG1lc3NhZ2UuZXJyb3IoJ+i/nuaOpea1i+ivleWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU0LCBudWxsLCBbWzAsIDhdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZVVzZXJNYW5hZ2U6IGZ1bmN0aW9uIGhhbmRsZVVzZXJNYW5hZ2UocmVjb3JkKSB7CiAgICAgIHRoaXMuY3VycmVudERhdGEgPSByZWNvcmQ7CiAgICAgIHRoaXMudXNlck1vZGFsVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlQWRkU3VibWl0OiBmdW5jdGlvbiBoYW5kbGVBZGRTdWJtaXQoKSB7CiAgICAgIHRoaXMuYWRkTW9kYWxWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlVXNlclN1Ym1pdDogZnVuY3Rpb24gaGFuZGxlVXNlclN1Ym1pdCgpIHsKICAgICAgdGhpcy51c2VyTW9kYWxWaXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uOwogICAgfSwKICAgIGhhbmRsZVNpemVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNpemVDaGFuZ2Uoc2l6ZSkgewogICAgICB0aGlzLnBhZ2luYXRpb24ucGFnZVNpemUgPSBzaXplOwogICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpOwogICAgfSwKICAgIGhhbmRsZVBhZ2VDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVBhZ2VDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSBwYWdlOwogICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpOwogICAgfQogIH0KfTs="}, null]}