import React, {Component, Fragment, createRef} from "react";
import {
    <PERSON>,
    Alert,
    Button,
    Table,
    Pagination,
    Row,
    Col,
    Form,
    Select,
    DatePicker,
    Input,
    Modal,
    Tabs,
    message,
    Icon
} from "antd";
import {connect} from "dva";
import AddUpdateModal from "./component/AddUpdateModal"
// import AuthDevice from "./component/AuthDevice"
import custom from "../../config/custom.js";
import styles from "./index.less";
import moment from "moment";
const { primaryColor } = custom;
const {Column} = Table;
const Option = Select.Option;
const FormItem = Form.Item;

@Form.create()
@connect(({}) => ({
}))
export default class AlarmEvent extends Component {
    constructor(props) {
        super(props);
        this.state = {
            currentPage: 1,
            currentPageSize: 10,
            selectedRowKeys: [],
            searchValue: {},
            totalRecords: 0,
            visiableAddUpdate:false,
            visiableAuth:false,
            authManagementList: [],
            type:1,
            loading:false
        };
    }

    componentWillUnmount() {
    }

    componentDidMount() {
        this.getAuthList();
    }

    /**
     * 批量状态更新
     */
    handUpdateClick = (record) => {
        const { dispatch } = this.props;   
        let selectedRowKeys = [];
        if (record.id) {
          selectedRowKeys.push(record.id);
        } else {
          selectedRowKeys = this.state.selectedRowKeys;
        }
        if (selectedRowKeys.length) {
          Modal.confirm({
            content: `确认更新选中数据的状态吗？`,
            okText: "确认",
            cancelText: "取消",
            onOk: () => {
              return new Promise((resolve,reject)=>{
                let params = {
                  ids: selectedRowKeys.join(),
                };      
                dispatch({
                  type: "authManagement/authorizeCheckData",
                  payload: params,
                  callback: (res) => {
                    if (res.retcode == 0) {
                      this.setState({
                        currentPage: 1,
                        headerShow: false,
                        selectedRowKeys: [],                       
                      });
                      message.success("批量状态更新成功");
                      this.getAuthList();
                    } else {
                      message.error(res.msg);                     
                    }
                    resolve()
                  },
                });
              }).catch((err)=>{
                console.log(err);
              })            
            }
          });
        } else {
          message.error("至少选中一条数据");
        }
    };

    /**
     * 新建更新
     */
    handleAdd = () => {
      this.setState({
        visiableAddUpdate:true,
        type:2
      })
    }
    handleAddCancel = () => {
        this.setState({
          visiableAddUpdate:false,
          visiableAuth:false
        })
    }

  /**
   * 设备列表分页大小改变事件
   * @param current
   * @param pageSize
   */
   onShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        currentPage: current,
        currentPageSize: pageSize,
      },
      () => {
        this.getAuthList();
      }
    );
  };


  /**
   * 页面改变处理事件
   * @param pageNumber
   */
  handlePageChange = (pageNumber) => {
    this.setState(
      {
        currentPage: pageNumber,
      },
      () => {
        this.getAuthList();
      }
    );
  };

    /**
     * 显示数据总数
     * @param total
     * @returns {string}
     */
    showTotal = total => {
        //获取总数状态
        const {authManagementList} = this.state;
        return `总数据${authManagementList.total}条`;
    };
    // 更新
    handleUpdate = () =>{
        this.setState({
          visiableAddUpdate:true,
          type:1
        })
    }



    getAuthList = ()=>{
      this.setState({
        loading:true
      })
        const { dispatch } = this.props;
        dispatch({
          type: "authManagement/authorizeSearchData",
          payload: {
            pageSize: this.state.currentPageSize,
            pageIndex: this.state.currentPage,
            licenceCategory: this.state.searchValue.licenceCategory,
            liceneStatus: this.state.searchValue.liceneStatus,
          },
          callback: (res) => {
            //判断是否成功
            if (res.retcode == 0) {
              this.setState({
                selectedRowKeys: [],
                authManagementList:res.data
              });
            } else {
              message.error(res.msg);
            }
            this.setState({
              loading:false
            })
          },
        });
    }

    /**
     * 搜索条件处理
     */
    handleAdvancedSearch = () => {
        const { form } = this.props;
        form.validateFields((err, values) => {
          if (!err) {
            let searchValue = {};
            if (values.liceneStatus) searchValue.liceneStatus = values.liceneStatus;
            if (values.licenceCategory) searchValue.licenceCategory = values.licenceCategory;
            this.setState(
              {
                searchValue: searchValue,
                currentPage: 1,
              },
              () => {
                this.getAuthList();
              }
            );
          }
        });
    };



    handleReset = () => {
        const { resetFields } = this.props.form;
        resetFields();
    }

  /**
   * 设备选择改变事件
   * @param selectedRowKeys
   */
   onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
    //   selectedDevList: selectedRows.map((item) => item.ip),
    });
  };
//   状态更新
  handleUpState = (record)=>{
    const { dispatch } = this.props;
    let selectedRowKeys = [];
    if (record.ip) {
      selectedRowKeys.push(record.id);
    } else {
      selectedRowKeys = this.state.selectedRowKeys;
    }
    if (selectedRowKeys.length) {
    Modal.confirm({
        // title: "删除",
        content: `确认更新这条数据吗？`,
        okText: "确认",
        cancelText: "取消",
        onOk: () => {
          let params = {
            ids: selectedRowKeys.join(),
          };      
          dispatch({
            type: "authManagement/authorizeCheckData",
            payload: params,
            callback: (res) => {
              if (res.retcode == 0) {
                this.setState({
                  currentPage: 1,             
                  selectedRowKeys: [],
                });
                message.success("更新成功");
                this.getAuthList();
              } else {
                message.error(res.msg);
              }
            },
          });
        }
      });
    } else {
      message.error("至少选中一条数据");
    }
  }

    render() {
        const {getFieldDecorator} = this.props.form;
        let {
            selectedRowKeys,
            currentPage,
            currentPageSize,
            authManagementList,
            totalRecords,
            visiableAddUpdate,
            visiableAuth,
            loading
        } = this.state;
       
        

        const rowSelection = {
            selectedRowKeys,
            onChange: this.onSelectChange,
          };
      
        return <Fragment>
          <Card style={{ borderRadius: 8 }} bordered={false}>
          <Form className="searchBg"    
              labelCol={{ span: 6}}
              wrapperCol={{ span: 14 }}>
              <Row >                 
                <Col span={8}>
                  <FormItem  label="授权状态:" colon={true}>
                    {getFieldDecorator("liceneStatus", {
                      initialValue: "",
                    })(<Select>
                        <Option value="">全部</Option>
                        <Option value="0">已启用</Option>
                        <Option value="1">未启用</Option>
                        <Option value="2">试用</Option>
                      </Select>)}
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem  label="许可证类型:" colon={true}>
                    {getFieldDecorator("licenceCategory", {
                      initialValue: "",
                    })(<Select>
                        <Option value="">全部</Option>
                        <Option value='IPS'>IPS</Option>
                        <Option value='AntiVirus'>AntiVirus</Option>
                      </Select>)}
                  </FormItem>
                </Col>
                <Col span={8} className="searchBtn">
                  <Button type="primary" loading={loading} onClick={this.handleAdvancedSearch}>
                    查询
                  </Button>
                  <Button style={{ marginLeft: 15 }} onClick={this.handleReset}>
                  清除
              </Button>          
                </Col>
              </Row>
            </Form>
          </Card>
          <div style={{ marginBottom: 20, marginTop: 20 }}>
            <Button type="primary" style={{ marginRight: 8 }} onClick={this.handleAdd}>
              新建授权
            </Button>
            <Button style={{ borderRadius: 2, display: this.state.authority == "审计员" ? "none" : "" }} onClick={this.handUpdateClick}>
              批量状态更新
            </Button>
          </div>
          <Card bordered={false} style={{ borderRadius: 8 }} className="TableContainer">               
            <Table rowSelection={rowSelection} className={styles.tableHeader} dataSource={authManagementList.rows} loading={loading} rowKey={(record) => record.id} pagination={false} locale={{ emptyText: "暂无数据信息" }} scroll={{ x: 1500 }}>
              <Column title="序号" dataIndex="key" key="key" width={50} 
                render= {(text, record, index) => {
                return `${index + 1}`;
              }}
              />
              <Column
                title="授权状态" 
                dataIndex="liceneStatus"
                key="liceneStatus"
                width={80} 
              
                />
              <Column title="设备名称" dataIndex="deviceName" key="deviceName" width={80} />
              <Column title="许可证类型" dataIndex="licenceCategory" key="licenceCategory" width={50} />
              <Column title="到期时间" dataIndex="expireDate" key="expireDate" width={70}  
              render={(text, record, index) => {
                  return `${moment(record.expireDate).format(
                  "YYYY/MM/DD"
              )}`;
              }}/>
              <Column title="获取设备许可证时间" dataIndex="createTime" key="createTime" width={70}
                render={(text, record, index) => {
                  return `${moment(record.createTime).format(
                  "YYYY/MM/DD"
                )}`
              }}/>
              {/* <Column title="最后一次更新时间" dataIndex="level_text" key="level_text" width={70} /> */}
              <Column
                title="操作"
                dataIndex="action"
                key="action"
                align= 'left'
                width={120}
                render={(text, record) => (
                  <div style={{ color: primaryColor }}>
                    <a
                      style={{ borderRadius: 2, marginLeft: 10, color: primaryColor }}
                      onClick={() => {
                        this.handUpdateClick(record);
                      }}
                    >
                      状态更新
                    </a>
                    <a
                      style={{ borderRadius: 2,  marginLeft: 10, color: primaryColor }}
                      onClick={() => {
                        this.handleUpdate(record);
                      }}
                    >
                      重新授权
                    </a>
                    {/* <a
                      style={{ borderRadius: 2,  marginLeft: 10, color: primaryColor }}
                      onClick={() => {
                        this.handleUpdate(record);
                      }}
                    >
                      删除
                    </a> */}
                  </div>
                )}
              />
            </Table>
          </Card>
          <Row type="flex" justify="end">
            <Pagination current={currentPage} showQuickJumper showSizeChanger onShowSizeChange={this.onShowSizeChange} total={authManagementList.total && parseInt(authManagementList.total)} showTotal={this.showTotal} onChange={this.handlePageChange} />
          </Row>
          {visiableAddUpdate && <AddUpdateModal visiableAddUpdate={visiableAddUpdate} handleAddCancel={this.handleAddCancel} getAuthList={this.getAuthList} typeAuth={this.state.type}/> }
        </Fragment>;
    }
}
