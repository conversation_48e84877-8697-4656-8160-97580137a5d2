{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue", "mtime": 1749799266715}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}