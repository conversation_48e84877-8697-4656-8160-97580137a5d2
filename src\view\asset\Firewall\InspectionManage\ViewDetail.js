import { Mo<PERSON>, Button ,message, Spin} from 'antd';
import moment from "moment";
import { inspectionInfo } from "../../pages/ResourceManagement/AddressSet/services";

class App extends React.Component {
  state = { visible: this.props.visiableDetail, recordList:[],loading:false };
  componentDidMount() {
    this.getInspectionData(this.props.recordData);
  }

  getInspectionData = async(record) => {
    try {
      this.setState({
        loading:true
      })
      const res = await inspectionInfo({ id: record.id });
      if (res.retcode == 0) {
        this.setState({recordList:res.data});
      } else {
        message.error(res.msg);
      }
      this.setState({
        loading:false
      })
    } catch (err) {
      console.log(err);
    }
  };


  render() {
    const {visible,recordList,loading} =this.state;
    return (     
      <div>           
        <Modal
          title="查看巡检详情"
          visible={visible}
          maskClosable={false}
          // onOk={this.handleOk}
          onCancel={()=>this.props.handleCancelView()}
          footer={[        
            <Button
              key={0}
              onClick={()=>this.props.handleCancelView()}
            >
              关闭
            </Button>,
          ]}
        >
          <Spin spinning={loading}>
          <div className="public-block">
            <div className="public-block-details">
              <div className="public-block-row">
                <span className="public-block-row-title">巡检类型：</span>
                {recordList.inspectionType==1?'手动':'自动'}
              </div>
              <div className="public-block-row">
                <span className="public-block-row-title">巡检日期：</span>
                {moment(recordList.inspectionDate).format("YYYY-MM-DD HH:mm:ss")}
              </div>
              <div className="public-block-row">
                <span className="public-block-row-title">巡检数量：</span>
                {recordList.totalCounts}
              </div>
            </div>
          </div>
          </Spin>  
        </Modal>        
      </div>
    )
  }
}
export default App