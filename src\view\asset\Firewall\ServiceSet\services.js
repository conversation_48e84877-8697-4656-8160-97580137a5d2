import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";

//服务集-查询接口
export async function fireRuleSearch(params) {
  return request(`/dev/fireRule/pages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//服务集-新增接口
export async function fireRuleAdd(params) {
  return request(`/dev/fireRule/add`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//服务集-查看
export async function fireRuleInfo(params) {
  return request(`/dev/fireRule/infor`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//服务集-编辑接口
export async function fireRuleUpdate(params) {
  return request(`/dev/fireRule/update`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//服务集-删除接口
export async function fireRuleDelete(params) {
  return request(`/dev/fireRule/delete`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//服务集-批量下发
export async function protocolIssued(params) {
  return request(`/dev/fireRule/protocolIssued`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//服务集-同步设备协议
export async function syncFromDevice(params) {
  return request(`/dev/fireRule/syncFromDevice`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}








//服务集-设备集合
export async function addInspectionData(params) {
  return request(`/dev/device/all`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

