import request from '@/util/request'

/**
 * 服务集-查询接口
 * @param params
 * @returns {Promise}
 */
export function getServiceSetList(params) {
  return request({
    url: '/dev/fireRule/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 服务集-新增接口
 * @param data
 * @returns {Promise}
 */
export function addServiceSet(data) {
  return request({
    url: '/dev/fireRule/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 服务集-编辑接口
 * @param data
 * @returns {Promise}
 */
export function updateServiceSet(data) {
  return request({
    url: '/dev/fireRule/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 服务集-查看
 * @param params
 * @returns {Promise}
 */
export function getServiceSetInfo(params) {
  return request({
    url: '/dev/fireRule/infor',
    method: 'post',
    data: params || {},
  })
}

/**
 * 服务集-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteServiceSet(data) {
  return request({
    url: '/dev/fireRule/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 服务集-批量下发
 * @param data
 * @returns {Promise}
 */
export function issueServiceSet(data) {
  return request({
    url: '/dev/fireRule/protocolIssued',
    method: 'post',
    data: data || {},
  })
}

/**
 * 服务集-同步设备协议
 * @param data
 * @returns {Promise}
 */
export function syncServiceSetFromDevice(data) {
  return request({
    url: '/dev/fireRule/syncFromDevice',
    method: 'post',
    data: data || {},
  })
}

/**
 * 服务集-获取设备集合
 * @param params
 * @returns {Promise}
 */
export function getDeviceList(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}
