export default [
  {
    path: '/firewall/device/list',
    name: 'FirewallDeviceList',
    component: () => import('@/view/asset/FirewallVue/DeviceList'),
    meta: { title: '设备列表', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/manage',
    name: 'FirewallGroupManage',
    component: () => import('@/view/asset/Hostguardiangroup/GroupManagement'),
    meta: { title: '分组管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/manage',
    name: 'FirewallInspectionManage',
    component: () => import('@/view/asset/FirewallVue/InspectionManage'),
    meta: { title: '巡检管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/auth/manage',
    name: 'FirewallAuthManage',
    component: () => import('@/view/asset/FirewallVue/AuthManage'),
    meta: { title: '授权管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/backup/restore',
    name: 'FirewallBackupRestore',
    component: () => import('@/view/asset/FirewallVue/BackupRestore'),
    meta: { title: '备份还原', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/upgrade/manage',
    name: 'FirewallUpgradeManage',
    component: () => import('@/view/asset/FirewallVue/UpgradeManage'),
    meta: { title: '升级管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/protocol/set',
    name: 'FirewallProtocolSet',
    component: () => import('@/view/asset/FirewallVue/ProtocolSet'),
    meta: { title: '工控协议集', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/service/set',
    name: 'FirewallServiceSet',
    component: () => import('@/view/asset/FirewallVue/ServiceSet'),
    meta: { title: '服务集', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/address/set',
    name: 'FirewallAddressSet',
    component: () => import('@/view/asset/FirewallVue/AddressSet'),
    meta: { title: '地址集', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/strategy/manage',
    name: 'FirewallStrategyManage',
    component: () => import('@/view/asset/FirewallVue/StrategyManage'),
    meta: { title: '策略管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/strategy-record',
    name: 'FirewallStrategyRecord',
    component: () => import('@/view/asset/FirewallVue/StrategyRecord'),
    meta: { title: '策略记录', icon: 'soc-icon-point' },
  },
]
