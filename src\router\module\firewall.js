export default [
  {
    path: '/firewall/device/list',
    name: 'FirewallDeviceList',
    component: () => import('@/view/asset/Firewall/DeviceList'),
    meta: { title: '设备列表', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/manage',
    name: 'FirewallGroupManage',
    component: () => import('@/view/asset/Firewall/GroupManage'),
    meta: { title: '分组管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/manage',
    name: 'FirewallInspectionManage',
    component: () => import('@/view/asset/Firewall/InspectionManage'),
    meta: { title: '巡检管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/auth/manage',
    name: 'FirewallAuthManage',
    component: () => import('@/view/asset/Firewall/AuthManage'),
    meta: { title: '授权管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/backup/restore',
    name: 'FirewallBackupRestore',
    component: () => import('@/view/asset/Firewall/BackupRestore'),
    meta: { title: '备份还原', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/upgrade/manage',
    name: 'FirewallUpgradeManage',
    component: () => import('@/view/asset/Firewall/UpgradeManage'),
    meta: { title: '升级管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/protocol/set',
    name: 'FirewallProtocolSet',
    component: () => import('@/view/asset/Firewall/ProtocolSet'),
    meta: { title: '工控协议集', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/service/set',
    name: 'FirewallServiceSet',
    component: () => import('@/view/asset/Firewall/ServiceSet'),
    meta: { title: '服务集', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/address/set',
    name: 'FirewallAddressSet',
    component: () => import('@/view/asset/Firewall/AddressSet'),
    meta: { title: '地址集', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/strategy/manage',
    name: 'FirewallStrategyManage',
    component: () => import('@/view/asset/Firewall/StrategyManage'),
    meta: { title: '策略管理', icon: 'soc-icon-point' },
  },
  {
    path: '/firewall/strategy-record',
    name: 'FirewallStrategyRecord',
    component: () => import('@/view/asset/Firewall/StrategyRecord'),
    meta: { title: '策略记录', icon: 'soc-icon-point' },
  },
]
