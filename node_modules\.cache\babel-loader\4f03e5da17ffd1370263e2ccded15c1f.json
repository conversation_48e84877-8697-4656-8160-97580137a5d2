{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\AddSentineUpdateModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\AddSentineUpdateModal.vue", "mtime": 1749800681690}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}