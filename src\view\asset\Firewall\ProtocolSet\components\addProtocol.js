import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  message,
  Form,
  Row,
  Col,
  Spin,
  Button,
  Select,
  Input,
  Radio,
} from "antd";
import SbrDrawer from "@/components/SbrDrawer";
import {industryProtocolInfo,  industryProtocolAdd, industryProtocolUpdate, functionCode } from "../services";

let Add = (props) => {
  const { refInstance, getSourceData } = props;
  const { getFieldDecorator, validateFields, setFieldsValue } = props.form;
  // 是否展示抽屉
  const [visible, setVisible] = useState(false);
  //标题
  const [title, setTitle] = useState('');
  // 查看数据
  const [record, setRecord] = useState({});
  const [loading, setLoading] = useState(false);
  // 功能码数据
  const [codeList, setCodeList] = useState([]);
  const [type, setType] = useState('modbus');
  useImperativeHandle(refInstance, () => ({
    showDrawer,
  }));

  useEffect(() => {}, []);


  const handleCode = async (value) => {
    try {
      setLoading(true);
      setType(value)
      setCodeList([]);    
      setFieldsValue({ functionCode: '' });
      const res = await functionCode({ type: value });
      if (res.retcode == 0) {
        if(value=='s7'){
          setCodeList(res.data.data.func);
        }else{
          setCodeList(res.data.data);
        }               
        setLoading(false);
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  };
  // 打开抽屉
  const showDrawer = async (record = {}) => {
    setVisible(true);   
    if (record.id) {
      handleCode(record.category)
      setTitle('编辑协议资源');
      try {
        setLoading(true);
        const res = await industryProtocolInfo({ id: record.id });
        if (res.retcode == 0) {        
          setFieldsValue({
            id: res.data.id,
            category: res.data.category,
            name: res.data.name,
            filterType: res.data.filterType,
            isFullCheck: res.data.isFullCheck,
            isReset: res.data.isReset,
            functionCode: res.data.functionCode,           
          });
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
      }
    }else{
      handleCode('modbus')
      setTitle('新增协议资源');
    }
  };

  //关闭抽屉
  const onDrawerClose = () => {
    setVisible(false);
  };
  // 提交
  const handleSubmit = (e) => {
    e.preventDefault();
    validateFields(async (err, values) => {
      if (!err) {
        let res 
        if(title.indexOf('新增')!=-1){
           res = await industryProtocolAdd(values);
        }else{
          res = await industryProtocolUpdate(values);
        }      
        if (res.retcode == 0) {
          message.success('操作成功');
          getSourceData();
          onDrawerClose();
        } else {
          message.error(res.msg);
        }
        setLoading(false);
      }
    });
  };

  return (
    <SbrDrawer
      title={title}
      width={800}
      onClose={onDrawerClose}
      visible={visible}
    >
      <Form
        onSubmit={handleSubmit}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <div className="public-block">
          <Spin spinning={loading}>
            <Form.Item hidden>
              {getFieldDecorator("id", {})(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
            <Form.Item label="协议">
              {getFieldDecorator("category", {
                initialValue:'modbus',
                rules: [
                  {
                    required: true,
                    message: "请选择协议",
                  },
                ],
              })(
                <Select placeholder="请选择协议" onChange={handleCode}>
                  <Option value="modbus">modbus</Option>
                  <Option value="iec104">iec104</Option>
                  <Option value="eip">eip</Option>
                  <Option value="dnp3">dnp3</Option>
                  <Option value="s7">s7</Option>
                  <Option value="mms">mms</Option> 
                </Select>
              )}
            </Form.Item>
            <Form.Item label="名称">
              {getFieldDecorator("name", {
                 rules: [
                  {
                    required: true,
                    message: "请输入名称",
                  },
                  {
                    pattern: /^[A-Za-z0-9-_]{1,15}$/,
                    message: '1-15个字母、数字、减号、下划线的组合。',
                  }                 
                ],
              })(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
            <Form.Item label="过滤机制">
              {getFieldDecorator("filterType", {
                 rules: [
                  {
                    required: true,
                    message: "请选择过滤机制",
                  },
                ],
              })(
                <Radio.Group onChange={() => {}}>
                  <Radio value={0} key={"0"}>
                    黑名单
                  </Radio>
                  <Radio value={1} key={"1"}>
                    白名单
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="完整性检查">
              {getFieldDecorator("isFullCheck", {
                  rules: [
                  {
                    required: true,
                    message: "请选择完整性检查",
                  },
                ],
              })(
                <Radio.Group onChange={() => {}}>
                  <Radio value={0} key={"0"}>
                    是
                  </Radio>
                  <Radio value={1} key={"1"}>
                    否
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="RESET">
              {getFieldDecorator("isReset", {
                  rules: [
                    {
                      required: true,
                      message: "请选择RESET",
                    },
                  ],
              })(
                <Radio.Group onChange={() => {}}>
                  <Radio value={0} key={"0"}>
                    是
                  </Radio>
                  <Radio value={1} key={"1"}>
                    否
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="功能码">
              {getFieldDecorator("functionCode", {
                  rules: [
                    {
                      required: true,
                      message: "请选择功能码",
                    },
                  ],
              })(
                <Select placeholder="请选择功能码">
                 {codeList.map((item)=>{
                  let code ='',name='';
                  switch(type){
                    
                    case 'modbus':
                         code =item.function_code
                         name =item.function_description
                         break
                    case 'iec104':
                          code =item.identifier
                          name =item.zh_describe
                          break;
                    case 'eip':
                          code =item.class_code
                          name =item.zh_desc
                          break;
                    case 'dnp3':
                          code =item.funcode
                          name =item.zh_describe
                          break;
                    case 's7':
                        code =item.function
                        name =item.function_description
                        break;
                    case 'mms':
                        code =item.pdu_code
                        name =item.en_desc
                        break;
                  }
                 return <Select.Option value={code + name} key={code + name}>
                  {code + name}
                </Select.Option>
                 })}
                </Select>
              )}
            </Form.Item>
          </Spin>
        </div>

        <Row type="flex" justify="center" className="drawer_btns">
          <Button htmlType="submit" type="primary">
            保存
          </Button>
          <Button className="spacing_btn" onClick={onDrawerClose}>
            关闭
          </Button>
        </Row>
      </Form>
    </SbrDrawer>
  );
};
Add = Form.create()(Add);
export default forwardRef((props, ref) => <Add {...props} refInstance={ref} />);
