{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\api\\sentine\\deviceManagement.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\api\\sentine\\deviceManagement.js", "mtime": 1750059601722}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}