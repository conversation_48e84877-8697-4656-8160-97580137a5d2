import request from '@/util/request'

/**
 * 工业协议集-查询接口
 * @param params
 * @returns {Promise}
 */
export function getProtocolSetList(params) {
  return request({
    url: '/dev/industryProtocol/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 工业协议集-新增接口
 * @param data
 * @returns {Promise}
 */
export function addProtocolSet(data) {
  return request({
    url: '/dev/industryProtocol/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 工业协议集-编辑接口
 * @param data
 * @returns {Promise}
 */
export function updateProtocolSet(data) {
  return request({
    url: '/dev/industryProtocol/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 工业协议集-查看
 * @param params
 * @returns {Promise}
 */
export function getProtocolSetInfo(params) {
  return request({
    url: '/dev/industryProtocol/infor',
    method: 'post',
    data: params || {},
  })
}

/**
 * 工业协议集-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteProtocolSet(data) {
  return request({
    url: '/dev/industryProtocol/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 工业协议集-批量下发
 * @param data
 * @returns {Promise}
 */
export function issueProtocolSet(data) {
  return request({
    url: '/dev/industryProtocol/protocolIssued',
    method: 'post',
    data: data || {},
  })
}

/**
 * 工业协议集-同步设备协议
 * @param data
 * @returns {Promise}
 */
export function syncProtocolSetFromDevice(data) {
  return request({
    url: '/dev/industryProtocol/syncFromDevice',
    method: 'post',
    data: data || {},
  })
}

/**
 * 工业协议集-切换协议
 * @param params
 * @returns {Promise}
 */
export function getFunctionCodeList(params) {
  return request({
    url: '/dev/functionCode/list',
    method: 'get',
    params: params || {},
  })
}

/**
 * 工业协议集-获取设备集合
 * @param params
 * @returns {Promise}
 */
export function getDeviceList(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}
