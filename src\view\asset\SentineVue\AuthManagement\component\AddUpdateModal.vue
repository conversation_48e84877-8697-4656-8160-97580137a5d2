<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="650px" :before-close="handleClose" @closed="onDialogClosed">
    <el-form ref="form" :model="formData" label-width="120px" :rules="rules">
      <el-form-item label="选择设备" prop="deviceIds">
        <el-select
          v-model="formData.deviceIds"
          placeholder="请选择设备"
          style="width: 100%"
          @change="handleDeviceChange"
        >
          <el-option
            v-for="device in flatDeviceList"
            :key="device.compId"
            :label="device.name"
            :value="device.compId"
            :disabled="device.type === '0'"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="许可证类型" prop="proc">
        <el-select v-model="formData.proc" placeholder="请选择许可证类型" style="width: 100%">
          <el-option label="IPS" value="IPS"></el-option>
          <el-option label="SOFT" value="SOFT"></el-option>
          <el-option label="AntiVirus" value="AntiVirus"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择授权文件" prop="uploadFile">
        <el-upload
          class="upload-demo"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          accept=".lic,.txt"
        >
          <el-button size="small" type="primary">
            <i class="el-icon-upload"></i> 导入
          </el-button>
          <div slot="tip" class="el-upload__tip">只能上传.lic/.txt文件，且不超过10MB</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addAuthorization } from '@/api/sentine/authManagement'
import { getDeviceTreeData } from '@/api/sentine/deviceManagement'

export default {
  name: 'AddUpdateModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '新建授权',
    },
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      formData: {
        deviceIds: '',
        proc: 'IPS',
        uploadFile: null,
      },
      deviceTreeData: [],
      flatDeviceList: [], // 扁平化的设备列表
      fileList: [],
      selectedDeviceId: '',
      rules: {
        deviceIds: [{ required: true, message: '请选择设备', trigger: 'change' }],
        proc: [{ required: true, message: '请选择许可证类型', trigger: 'change' }],
        uploadFile: [{ required: true, message: '请选择授权文件', trigger: 'change' }],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.resetFormData()
        this.getDeviceTreeData()
      }
    },
  },
  methods: {
    resetFormData() {
      this.formData = {
        deviceIds: '',
        proc: 'IPS',
        uploadFile: null,
      }
      this.fileList = []
      this.selectedDeviceId = ''
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    onDialogClosed() {
      this.$refs.form && this.$refs.form.resetFields()
    },
    async getDeviceTreeData() {
      try {
        const res = await getDeviceTreeData({ category: 4 })
        if (res.retcode === 0) {
          this.deviceTreeData = res.data || []
          this.flatDeviceList = this.flattenDeviceTree(this.deviceTreeData)
        } else {
          this.$message.error(res.msg || '获取设备树失败')
        }
      } catch (error) {
        this.$message.error('获取设备树失败')
      }
    },
    // 将设备树扁平化为列表
    flattenDeviceTree(treeData) {
      const result = []
      const traverse = (nodes) => {
        nodes.forEach(node => {
          result.push({
            compId: node.compId,
            name: node.name,
            type: node.type,
            srcId: node.srcId
          })
          if (node.childList && node.childList.length > 0) {
            traverse(node.childList)
          }
        })
      }
      traverse(treeData)
      return result
    },
    handleDeviceChange(value) {
      // 根据选中的设备ID找到对应的设备信息
      const selectedDevice = this.flatDeviceList.find(device => device.compId === value)
      if (selectedDevice && selectedDevice.type === '1') {
        this.selectedDeviceId = selectedDevice.srcId
      }
    },
    handleFileChange(file, fileList) {
      const isValidType = file.raw.type === 'text/plain' || file.name.endsWith('.lic')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传.lic或.txt格式的文件!')
        this.fileList = []
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        this.fileList = []
        return false
      }

      this.formData.uploadFile = file.raw
      this.fileList = fileList
    },
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true

          try {
            // 构建请求参数，匹配React版本的逻辑
            const payload = {
              proc: this.formData.proc,
              deviceId: this.selectedDeviceId,
              uploadFile: this.formData.uploadFile,
            }

            const res = await addAuthorization(payload)
            if (res.retcode === 0) {
              this.$message.success('添加成功!')
              this.$emit('on-submit')
              this.handleClose()
            } else {
              this.$message.error(res.msg || '授权添加失败')
            }
          } catch (error) {
            this.$message.error('授权添加失败')
          } finally {
            this.submitLoading = false
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.upload-demo {
  width: 100%;
}
</style>
