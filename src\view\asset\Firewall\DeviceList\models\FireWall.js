import { getFireWallListData,
	     addEquipment,
	     editEquipment,
	     getAuthUser,
	     deleteFireWallListData,
	     getUser,
	     deleteUser,
	     modifyDefLoginAccount,
	     getAuthDataList,
	     loginForLog} from '@/services/FireWall/fireWall';
import { getTopoMapData, setTopoMapData } from "@/services/NetTopoMap/netTopoMap";
import { devicePing } from "@/services/api";

export default {
	namespace: 'fireWall',

	state: {
		fireWallData:{},
		topoData: {},
		getUserList:[],
		getAuthList: ''
	},

	effects: {
		*getData({ payload, callback }, { call, put }) {
			try {
				const response = yield call(getFireWallListData, payload);

				yield put({
					type: 'saveData',
					fireWallData: response.data,
				})
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*getTopoData({payload,callback},{call,put}){
			try {
				const response = yield call(getTopoMapData);
				yield put({
					type: 'saveData',
					topoData: response.data,
				});
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*setTopoData({payload,callback},{call,put}){
			try {
				const response = yield call(setTopoMapData, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*addEquipment({ payload, callback }, { call, put }) {
			try {
				const response = yield call(addEquipment, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*editEquipment({ payload, callback }, { call, put }) {
			try {
				const response = yield call(editEquipment, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*DeleteData({ payload, callback }, { call, put }) {
			try {
				const response = yield call(deleteFireWallListData, payload);
				const jsonData = JSON.parse(response)
				if (callback) callback(jsonData)
			} catch (err) {
				console.log(err)
			}
		},
		*getUser({ payload, callback }, { call, put }) {
			try {
				const response = yield call(getUser, payload);
				yield put({
					type: 'saveData',
					getUserList: response.data.items,
				})
				if (callback) callback(response)
			} catch (err) {
				console.log(err)
			}
		},
		*deleteUser({ payload, callback }, { call, put }) {
			try {
				const response = yield call(deleteUser, payload);
				const jsonData = JSON.parse(response)
				if (callback) callback(jsonData)
			} catch (err) {
				console.log(err)
			}
		},
		*getAuth({ payload, callback }, { call, put }) {
			try {
				const response = yield call(getAuthDataList, payload);
				yield put({
					type: 'saveData',
					getAuthList: response.data.auth,
				})
				if (callback) callback(response)
			} catch (err) {
				console.log(err)
			}
		},
		*addUser({ payload, callback }, { call, put }) {
			try {
				const response = yield call(getAuthUser, payload);
				if (callback) callback(response)
			} catch (err) {
				console.log(err)
			}
		},
		*modifyUser({payload,callback},{call,put}){
			try {
				const response = yield call(modifyDefLoginAccount, payload);
				if (callback) callback(response)
			} catch (err) {
				console.log(err)
			}
		},
		*loginForLog({payload},{call,put}){
			try {
				const response = yield call(loginForLog, payload);
			} catch (err) {
				console.log(err)
			}
		},
		*clearData({ payload, callback }, { call, put }) {
			yield put({
				type: 'saveData',
				fireWallData:{},
				getUserList:[],
				getAuthList: ''
			})
		},
		*devicePing({payload, callback},{call, put}){
			try {
				const response = yield call(devicePing, payload);
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		}
	},

	reducers: {
		saveData(state, action) {
			return {
				...state, ...action
			}
		}
	}
}