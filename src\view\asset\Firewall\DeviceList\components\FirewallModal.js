import React, { Component, Fragment } from "react";
import { Modal, Form, Input, Button, TreeSelect} from "antd";
import IPut from "@/components/IPInput/IPInput";

const FormItem = Form.Item;
const { TreeNode } = TreeSelect;

@Form.create()
export default class AddDeviceModal extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      value: 1,
      loading: false,
      showTitle: false
    };
  }

  renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.childList) {
        return (
          <TreeNode title={item.groupName} key={item.id} value={item.id}>
            {this.renderTreeNodes(item.childList)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.groupName} key={item.id} value={item.id} />;
    });
  };

  onChange = (e) => {
    if (e.target.value == 2) {
      this.setState({});
    } else {
      this.setState({});
    }
    this.setState({
      value: e.target.value
    });

  };


  renderHeader = () => (
    <div className='modalTitle'>
      <h4>添加新设备</h4>
    </div>
  );

  onShow = () => {
    this.setState({
      visible: true
    });
  };


  onHide = () =>{
    this.setState({
      visible: false
    })
  }

  loading = () =>{
    this.setState({
      loading: true
    })
  }

  unLoading = () =>{
    this.setState({
      loading: false
    })
  }

  handleCancelClick = () => {
    this.setState({
      visible: false,
      showTitle: false
    });
  };


  inputChange = (val) => {
    this.setState({
      showTitle: val == "..."
    });
  };


  render() {
    const { visible, loading, showTitle } = this.state;
    const { form:{getFieldDecorator},saveOnClick } = this.props;
    return (
      <Fragment>
        <Modal
          visible={visible}
          title={this.renderHeader()}
          destroyOnClose
          centered
          width={650}
          onCancel={this.handleCancelClick}
          maskClosable={false}
          footer={[
            <Button key="back" onClick={this.handleCancelClick}>
              取消
            </Button>,
            <Button key="submit" type="primary" loading={loading} onClick={saveOnClick.bind(this,this.props.form)}>
              确认
            </Button>
          ]}
        >
          <Form>
            <FormItem
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              label="设备名称："
            >
              {getFieldDecorator("name", {
                rules: [{
                  required: true,
                  message: '请输入设备名称'
                },{
                  pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,
                  message: "请输入汉字、字母、数字、短横线或下划线"
                }]
              })(
                <Input maxLength={50} placeholder='请输入名称'/>
              )}
            </FormItem>
            <FormItem labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} label="设备分组:">
              {getFieldDecorator("groupName", {
                rules: [
                  {
                    required: true,
                    message: "请输入IP地址",
                  },
                ],              
              })(
                  <TreeSelect
                  style={{ width: '100%' }}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                  placeholder="请选择分组"
                >
                  {this.renderTreeNodes(this.props.groupData)}
                </TreeSelect>
              )}
            </FormItem>
            <FormItem
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              label="设备IP："
            >
              {getFieldDecorator("ip", {
                rules: [{
                  required: true,
                  message: "请输入IP地址"
                }],
                initialValue: ""
              })(
                <IPut maxLength={50} onChange={this.inputChange} placeholder='请输入IP地址'/>
              )}
              <span style={{ display: showTitle ? "block" : "none", color: "red" }}>IP地址不能为空</span>
            </FormItem>
          </Form>

        </Modal>
      </Fragment>
    );
  }
}