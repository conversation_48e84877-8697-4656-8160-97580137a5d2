{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue", "mtime": 1750140426768}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEZpcmV3YWxsRGV2aWNlTGlzdCwgZGVsZXRlRmlyZXdhbGxEZXZpY2UsIGJhdGNoRGVsZXRlRmlyZXdhbGxEZXZpY2UsIGRldmljZVBpbmcsIGdldFRvcG9EYXRhLCBzZXRUb3BvRGF0YSB9IGZyb20gJ0AvYXBpL2ZpcmV3YWxsL2RldmljZU1hbmFnZW1lbnQnCmltcG9ydCBBZGREZXZpY2VNb2RhbCBmcm9tICcuL2NvbXBvbmVudHMvQWRkRGV2aWNlTW9kYWwudnVlJwppbXBvcnQgVXNlck1hbmFnZU1vZGFsIGZyb20gJy4vY29tcG9uZW50cy9Vc2VyTWFuYWdlTW9kYWwudnVlJwoKLy8g55Sf5oiQ5ZSv5LiASUTnmoTlt6Xlhbflh73mlbAKZnVuY3Rpb24gZ3VpZCgpIHsKICByZXR1cm4gJ3h4eHh4eHh4LXh4eHgtNHh4eC15eHh4LXh4eHh4eHh4eHh4eCcucmVwbGFjZSgvW3h5XS9nLCBmdW5jdGlvbihjKSB7CiAgICBjb25zdCByID0gTWF0aC5yYW5kb20oKSAqIDE2IHwgMAogICAgY29uc3QgdiA9IGMgPT09ICd4JyA/IHIgOiAociAmIDB4MyB8IDB4OCkKICAgIHJldHVybiB2LnRvU3RyaW5nKDE2KQogIH0pCn0KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGV2aWNlTGlzdCcsCiAgY29tcG9uZW50czogewogICAgQWRkRGV2aWNlTW9kYWwsCiAgICBVc2VyTWFuYWdlTW9kYWwsCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlVGFiOiAnMCcsCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBxdWVyeUlucHV0OiB7CiAgICAgICAgZmlyZU5hbWU6ICcnLAogICAgICAgIG9yaWdpbklwOiAnJywKICAgICAgICBvbmxpblN0YXR1czogJycsCiAgICAgIH0sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIHNlbGVjdGVkUm93czogW10sCiAgICAgIHBhZ2luYXRpb246IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgdmlzaWJsZTogdHJ1ZSwKICAgICAgfSwKICAgICAgYWRkTW9kYWxWaXNpYmxlOiBmYWxzZSwKICAgICAgdXNlck1vZGFsVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnREYXRhOiBudWxsLAogICAgICBwYW5lczogW10sIC8vIOWKqOaAgeagh+etvumhtQogICAgICB0aW1lcjogbnVsbCwgLy8g5a6a5pe25ZmoCiAgICAgIHRvcG9EYXRhOiB7fSwgLy8g5a2Y5YKo5ouT5omR5pWw5o2uCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoKQogICAgdGhpcy5zdGFydFRpbWVyKCkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLmNsZWFyVGltZXIoKQogIH0sCiAgbWV0aG9kczogewogICAgdG9nZ2xlU2hvdygpIHsKICAgICAgdGhpcy5pc1Nob3cgPSAhdGhpcy5pc1Nob3cKICAgIH0sCiAgICBzdGFydFRpbWVyKCkgewogICAgICAvLyDmr48zMOenkuWIt+aWsOS4gOasoeiuvuWkh+WIl+ihqAogICAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkKICAgICAgfSwgMzAwMDApCiAgICB9LAogICAgY2xlYXJUaW1lcigpIHsKICAgICAgaWYgKHRoaXMudGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMudGltZXIpCiAgICAgICAgdGhpcy50aW1lciA9IG51bGwKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGdldEZpcmV3YWxsRGV2aWNlTGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCBwYXlsb2FkID0gewogICAgICAgIF9saW1pdDogdGhpcy5wYWdpbmF0aW9uLnBhZ2VTaXplLAogICAgICAgIF9wYWdlOiB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UsCiAgICAgICAgcXVlcnlQYXJhbXM6IHRoaXMuYnVpbGRRdWVyeVBhcmFtcygpLAogICAgICAgIHR5cGU6IDEsCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KHBheWxvYWQpCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHJlcy5kYXRhLml0ZW1zIHx8IFtdCiAgICAgICAgICB0aGlzLnBhZ2luYXRpb24udG90YWwgPSByZXMuZGF0YS50b3RhbCB8fCAwCiAgICAgICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluiuvuWkh+WIl+ihqOWksei0pScpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIGJ1aWxkUXVlcnlQYXJhbXMoKSB7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHt9CiAgICAgIGlmICh0aGlzLnF1ZXJ5SW5wdXQuZmlyZU5hbWUpIHBhcmFtcy5maXJlTmFtZSA9IHRoaXMucXVlcnlJbnB1dC5maXJlTmFtZQogICAgICBpZiAodGhpcy5xdWVyeUlucHV0Lm9yaWdpbklwKSBwYXJhbXMub3JpZ2luSXAgPSB0aGlzLnF1ZXJ5SW5wdXQub3JpZ2luSXAKICAgICAgaWYgKHRoaXMucXVlcnlJbnB1dC5vbmxpblN0YXR1cyAhPT0gJycpIHBhcmFtcy5vbmxpblN0YXR1cyA9IHRoaXMucXVlcnlJbnB1dC5vbmxpblN0YXR1cwogICAgICByZXR1cm4gcGFyYW1zCiAgICB9LAogICAgaGFuZGxlVGFiQ2xpY2sodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiLm5hbWUKICAgICAgaWYgKHRhYi5uYW1lICE9PSAnMCcpIHsKICAgICAgICAvLyDlpITnkIbliqjmgIHmoIfnrb7pobXngrnlh7sKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkKICAgIH0sCiAgICBoYW5kbGVSZXNldCgpIHsKICAgICAgdGhpcy5xdWVyeUlucHV0ID0gewogICAgICAgIGZpcmVOYW1lOiAnJywKICAgICAgICBvcmlnaW5JcDogJycsCiAgICAgICAgb25saW5TdGF0dXM6ICcnLAogICAgICB9CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5jdXJyZW50RGF0YSA9IG51bGwKICAgICAgdGhpcy5hZGRNb2RhbFZpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlRWRpdChyZWNvcmQpIHsKICAgICAgdGhpcy5jdXJyZW50RGF0YSA9IHJlY29yZAogICAgICB0aGlzLmFkZE1vZGFsVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICBoYW5kbGVWaWV3KHJlY29yZCkgewogICAgICAvLyDmiZPlvIDorr7lpIfnrqHnkIbpobXpnaIKICAgICAgY29uc3QgcGFuZSA9IHsKICAgICAgICB0aXRsZTogcmVjb3JkLmZpcmVOYW1lLAogICAgICAgIG5hbWU6IHJlY29yZC5pcCwKICAgICAgICBjb250ZW50OiBgaHR0cHM6Ly8ke3JlY29yZC5vcmlnaW5JcH06JHtyZWNvcmQucG9ydH1gLAogICAgICB9CgogICAgICAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/miZPlvIAKICAgICAgY29uc3QgZXhpc3RpbmdQYW5lID0gdGhpcy5wYW5lcy5maW5kKHAgPT4gcC5uYW1lID09PSBwYW5lLm5hbWUpCiAgICAgIGlmICghZXhpc3RpbmdQYW5lKSB7CiAgICAgICAgdGhpcy5wYW5lcy5wdXNoKHBhbmUpCiAgICAgIH0KICAgICAgdGhpcy5hY3RpdmVUYWIgPSBwYW5lLm5hbWUKICAgIH0sCiAgICBoYW5kbGVEZWxldGUocmVjb3JkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOivpeiuvuWkh+WQlz/liKDpmaTlkI7kuI3lj6/mgaLlpI0nLCAn5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu6K6kJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgIH0pCiAgICAgICAgLnRoZW4oYXN5bmMgKCkgPT4gewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZGVsZXRlRmlyZXdhbGxEZXZpY2UoeyBkZXZpY2VfaWQ6IHJlY29yZC5pZCB9KQogICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpCiAgICAgICAgICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgICAgICAgICAgIC8vIOS7juaLk+aJkeS4reWIoOmZpOiuvuWkh+iKgueCuQogICAgICAgICAgICAgIHRoaXMuZGVsRGV2aWNlTm9kZShbcmVjb3JkLm9yaWdpbklwXSkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4ge30pCiAgICB9LAogICAgaGFuZGxlQmF0Y2hEZWxldGUoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfoh7PlsJHpgInkuK3kuIDmnaHmlbDmja4nKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTpgInkuK3orr7lpIflkJc/5Yig6Zmk5ZCO5LiN5Y+v5oGi5aSNJywgJ+WIoOmZpCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruiupCcsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICB9KQogICAgICAgIC50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGNvbnN0IGlkcyA9IHRoaXMuc2VsZWN0ZWRSb3dzLm1hcChyb3cgPT4gcm93LmlkKQogICAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBiYXRjaERlbGV0ZUZpcmV3YWxsRGV2aWNlKHsgZGV2aWNlX2lkczogaWRzIH0pCiAgICAgICAgICAgIGlmIChyZXMucmV0Y29kZSA9PT0gMCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpCiAgICAgICAgICAgICAgLy8g5LuO5ouT5omR5Lit5om56YeP5Yig6Zmk6K6+5aSH6IqC54K5CiAgICAgICAgICAgICAgY29uc3QgZGV2aWNlSXBzID0gdGhpcy5zZWxlY3RlZFJvd3MubWFwKHJvdyA9PiByb3cub3JpZ2luSXApCiAgICAgICAgICAgICAgdGhpcy5kZWxEZXZpY2VOb2RlKGRldmljZUlwcykKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4ge30pCiAgICB9LAogICAgYXN5bmMgaGFuZGxlUGluZyhyZWNvcmQpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOa1i+ivlei/nuaOpS4uLicpCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZGV2aWNlUGluZyh7IGlwOiByZWNvcmQub3JpZ2luSXAgfSkKICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6K6+5aSH6L+e5o6l5q2j5bi4JykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K6+5aSH6L+e5o6l5aSx6LSlJykKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6L+e5o6l5rWL6K+V5aSx6LSlJykKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVVzZXJNYW5hZ2UocmVjb3JkKSB7CiAgICAgIHRoaXMuY3VycmVudERhdGEgPSByZWNvcmQKICAgICAgdGhpcy51c2VyTW9kYWxWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZUFkZFN1Ym1pdChkZXZpY2VEYXRhKSB7CiAgICAgIHRoaXMuYWRkTW9kYWxWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgICAvLyDlpoLmnpzmnInorr7lpIfmlbDmja7vvIzmt7vliqDliLDmi5PmiZHkuK0KICAgICAgaWYgKGRldmljZURhdGEgJiYgZGV2aWNlRGF0YS5pcCkgewogICAgICAgIHRoaXMuYWRkRGV2aWNlTm9kZShkZXZpY2VEYXRhKQogICAgICB9CiAgICB9LAogICAgaGFuZGxlVXNlclN1Ym1pdCgpIHsKICAgICAgdGhpcy51c2VyTW9kYWxWaXNpYmxlID0gZmFsc2UKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uCiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5wYWdlU2l6ZSA9IHNpemUKICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgfSwKICAgIGhhbmRsZVBhZ2VDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSBwYWdlCiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkKICAgIH0sCgogICAgLy8gPT09PT09PT09PT09PT09PT09PT0g5ouT5omR55u45YWz5pa55rOVID09PT09PT09PT09PT09PT09PT09CgogICAgLyoqCiAgICAgKiDojrflj5bmi5PmiZHmlbDmja4KICAgICAqLwogICAgYXN5bmMgZ2V0VG9wb0RhdGFGcm9tU2VydmVyKGNhbGxiYWNrKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0VG9wb0RhdGEoKQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgdGhpcy50b3BvRGF0YSA9IHJlcy5kYXRhIHx8IHsgbm9kZXM6IFtdLCBlZGdlczogW10gfQogICAgICAgICAgaWYgKGNhbGxiYWNrKSBjYWxsYmFjaygpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaLk+aJkeaVsOaNruWksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLyoqCiAgICAgKiDkv53lrZjmi5PmiZHmlbDmja4KICAgICAqIEBwYXJhbSB0b3BvRGF0YQogICAgICovCiAgICBhc3luYyBzYXZlVG9wb0RhdGFUb1NlcnZlcih0b3BvRGF0YSkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHNldFRvcG9EYXRhKHsKICAgICAgICAgIHRvcG9sb2d5X3RleHQ6IHRvcG9EYXRhLAogICAgICAgIH0pCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOaLk+aJkeaVsOaNruWksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLyoqCiAgICAgKiDnlJ/miJDorr7lpIfoioLngrkKICAgICAqLwogICAgZ2VuZXJhdGVOb2RlKG9wdGlvbnMpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBpZDogZ3VpZCgpLAogICAgICAgIHR5cGU6ICdub2RlJywKICAgICAgICBzaXplOiAnNTAnLAogICAgICAgIHNoYXBlOiAna29uaS1jdXN0b20tbm9kZScsCiAgICAgICAgY29sb3I6ICcjNjlDMEZGJywKICAgICAgICBsYWJlbE9mZnNldFk6IDM4LAogICAgICAgIC4uLm9wdGlvbnMsCiAgICAgIH0KICAgIH0sCgogICAgLyoqCiAgICAgKiDmt7vliqDorr7lpIfoioLngrnlip/og70KICAgICAqIEBwYXJhbSB2YWx1ZXMKICAgICAqLwogICAgYWRkRGV2aWNlTm9kZSh2YWx1ZXMpIHsKICAgICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoKCkgPT4gewogICAgICAgIGNvbnN0IHRvcG9EYXRhID0geyAuLi50aGlzLnRvcG9EYXRhIH0KCiAgICAgICAgLy8g56Gu5L+dIG5vZGVzIOWSjCBlZGdlcyDmlbDnu4TlrZjlnKgKICAgICAgICBpZiAoIXRvcG9EYXRhLm5vZGVzKSB0b3BvRGF0YS5ub2RlcyA9IFtdCiAgICAgICAgaWYgKCF0b3BvRGF0YS5lZGdlcykgdG9wb0RhdGEuZWRnZXMgPSBbXQoKICAgICAgICAvLyAxLiDliKTmlq3ot6/nlLHlmajmmK/lkKblrZjlnKjvvIzkuI3lrZjlnKjnlJ/miJDot6/nlLHlmagKICAgICAgICBjb25zdCB7IGlwIH0gPSB2YWx1ZXMKICAgICAgICBjb25zdCBpcGxpc3QgPSBpcC5zcGxpdCgnLicpCiAgICAgICAgaXBsaXN0LnBvcCgpCiAgICAgICAgY29uc3Qgcm91dGVySXAgPSBpcGxpc3Quam9pbignLicpICsgJy4wJwoKICAgICAgICBjb25zdCBpc0V4aXN0ID0gdG9wb0RhdGEubm9kZXMubGVuZ3RoID4gMCAmJgogICAgICAgICAgdG9wb0RhdGEubm9kZXMuc29tZSh2YWwgPT4gdmFsLmRldmljZV9pcCA9PT0gcm91dGVySXApCgogICAgICAgIGlmICghaXNFeGlzdCkgewogICAgICAgICAgY29uc3QgbmV3Tm9kZSA9IHRoaXMuZ2VuZXJhdGVOb2RlKHsKICAgICAgICAgICAgZGV2aWNlX2lwOiByb3V0ZXJJcCwKICAgICAgICAgICAgY2F0ZWdvcnk6IDMsCiAgICAgICAgICAgIGxhYmVsOiAn6Lev55Sx5ZmoJwogICAgICAgICAgfSkKICAgICAgICAgIHRvcG9EYXRhLm5vZGVzLnB1c2gobmV3Tm9kZSkKICAgICAgICB9CgogICAgICAgIC8vIDIuIOWIpOaWreiuvuWkh+aYr+WQpuWtmOWcqO+8jOS4jeWtmOWcqOWwseeUn+aIkOiuvuWkh+iKgueCueWSjOi/nue6v+i+uQogICAgICAgIGNvbnN0IGlzRGV2RXhpc3QgPSB0b3BvRGF0YS5ub2Rlcy5sZW5ndGggPiAwICYmCiAgICAgICAgICB0b3BvRGF0YS5ub2Rlcy5zb21lKHZhbCA9PiB2YWwuZGV2aWNlX2lwID09PSB2YWx1ZXMuaXApCgogICAgICAgIGlmICghaXNEZXZFeGlzdCkgewogICAgICAgICAgY29uc3QgbmV3Tm9kZSA9IHRoaXMuZ2VuZXJhdGVOb2RlKHsKICAgICAgICAgICAgbGFiZWw6ICfpmLLngavlopknLAogICAgICAgICAgICBjYXRlZ29yeTogMSwKICAgICAgICAgICAgZGV2aWNlX2lwOiB2YWx1ZXMuaXAsCiAgICAgICAgICB9KQogICAgICAgICAgdG9wb0RhdGEubm9kZXMucHVzaChuZXdOb2RlKQoKICAgICAgICAgIGNvbnN0IHNlcnZlckRhdGEgPSB0b3BvRGF0YS5ub2Rlcy5maW5kKHZhbCA9PiB2YWwuZGV2aWNlX2lwID09PSByb3V0ZXJJcCkKICAgICAgICAgIGlmIChzZXJ2ZXJEYXRhKSB7CiAgICAgICAgICAgIHRvcG9EYXRhLmVkZ2VzLnB1c2goewogICAgICAgICAgICAgIGlkOiBndWlkKCksCiAgICAgICAgICAgICAgc291cmNlOiBzZXJ2ZXJEYXRhLmlkLAogICAgICAgICAgICAgIHRhcmdldDogbmV3Tm9kZS5pZAogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgdGhpcy5zYXZlVG9wb0RhdGFUb1NlcnZlcih0b3BvRGF0YSkKICAgICAgfSkKICAgIH0sCgogICAgLyoqCiAgICAgKiDliKDpmaTorr7lpIfoioLngrnlip/og70KICAgICAqIEBwYXJhbSBzZWxlY3RlZERldkxpc3QKICAgICAqLwogICAgZGVsRGV2aWNlTm9kZShzZWxlY3RlZERldkxpc3QpIHsKICAgICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoKCkgPT4gewogICAgICAgIGNvbnN0IHRvcG9EYXRhID0geyAuLi50aGlzLnRvcG9EYXRhIH0KCiAgICAgICAgLy8g56Gu5L+dIG5vZGVzIOWSjCBlZGdlcyDmlbDnu4TlrZjlnKgKICAgICAgICBpZiAoIXRvcG9EYXRhLm5vZGVzKSB0b3BvRGF0YS5ub2RlcyA9IFtdCiAgICAgICAgaWYgKCF0b3BvRGF0YS5lZGdlcykgdG9wb0RhdGEuZWRnZXMgPSBbXQoKICAgICAgICAvLyAxLiDliKTmlq3orr7lpIfmmK/lkKblrZjlnKjvvIzlrZjlnKjlsLHliKDpmaToioLngrnlkozov57nur/ovrkKICAgICAgICBzZWxlY3RlZERldkxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRvcG9EYXRhLm5vZGVzLmZpbmQodmFsID0+IHZhbC5kZXZpY2VfaXAgPT09IGl0ZW0pCiAgICAgICAgICBpZiAocmVzdWx0KSB7CiAgICAgICAgICAgIHRvcG9EYXRhLm5vZGVzID0gdG9wb0RhdGEubm9kZXMuZmlsdGVyKG5vZGUgPT4gbm9kZS5pZCAhPT0gcmVzdWx0LmlkKQogICAgICAgICAgICB0b3BvRGF0YS5lZGdlcyA9IHRvcG9EYXRhLmVkZ2VzLmZpbHRlcihlZGdlID0+IGVkZ2UudGFyZ2V0ICE9PSByZXN1bHQuaWQpCiAgICAgICAgICB9CiAgICAgICAgfSkKCiAgICAgICAgLy8gMi4g6I635Y+W6Lev55Sx5Zmo5YiX6KGoCiAgICAgICAgY29uc3Qgcm91dGVyTGlzdCA9IHRvcG9EYXRhLm5vZGVzLmZpbHRlcihpdGVtID0+IGl0ZW0uY2F0ZWdvcnkgPT09IDMpCgogICAgICAgIC8vIDMuIOWIpOaWrei3r+eUseWZqOaYr+WQpuWtpOeri++8jOWmguaenOWtpOeri+WImeWIoOmZpAogICAgICAgIHJvdXRlckxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGNvbnN0IGlzRGVsID0gdG9wb0RhdGEuZWRnZXMuZXZlcnkodmFsID0+IHZhbC5zb3VyY2UgIT09IGl0ZW0uaWQpCiAgICAgICAgICBpZiAoaXNEZWwpIHsKICAgICAgICAgICAgdG9wb0RhdGEubm9kZXMgPSB0b3BvRGF0YS5ub2Rlcy5maWx0ZXIobm9kZSA9PiBub2RlLmlkICE9PSBpdGVtLmlkKQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIHRoaXMuc2F2ZVRvcG9EYXRhVG9TZXJ2ZXIodG9wb0RhdGEpCiAgICAgIH0pCiAgICB9LAogIH0sCn0K"}, null]}