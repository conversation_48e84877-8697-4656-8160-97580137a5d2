<template>
  <div class="router-wrap-table">
    <div class="page-title">策略记录</div>
    
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.name" clearable placeholder="策略名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.name" clearable placeholder="策略名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="queryInput.recordTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  @change="handleQuery"
                />
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.operateType" clearable placeholder="操作类型" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="下发" value="0"></el-option>
                  <el-option label="同步" value="1"></el-option>
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.status" clearable placeholder="状态" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="成功" value="1"></el-option>
                  <el-option label="失败" value="0"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">策略记录管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="名称"></el-table-column>
          <el-table-column prop="addTime" label="时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.addTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="scope.row.status == '0' ? 'status-failed' : 'status-success'">
                {{ getStatusText(scope.row) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="operateType" label="操作类型">
            <template slot-scope="scope">
              {{ scope.row.operateType == "0" ? "下发" : "同步" }}
            </template>
          </el-table-column>
          <el-table-column prop="counts" label="操作数量"></el-table-column>
          <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>
  </div>
</template>

<script>
import { getStrategyRecordList, deleteStrategyRecord } from '@/api/firewall/strategyRecord'
import dayjs from 'dayjs'

export default {
  name: 'StrategyRecord',
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        name: '',
        recordTime: null,
        operateType: '',
        status: ''
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true
      }
    }
  },
  mounted() {
    this.getStrategyRecordList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getStrategyRecordList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams()
      }

      try {
        const res = await getStrategyRecordList(payload)
        if (res.retcode === 0) {
          // 根据原始React版本，数据直接在res.data中，但需要检查是否有分页结构
          if (res.data && Array.isArray(res.data)) {
            // 如果data是数组，直接使用
            this.tableData = res.data
            this.pagination.total = res.data.length
          } else if (res.data && res.data.rows) {
            // 如果data有rows属性，使用rows和total
            this.tableData = res.data.rows || []
            this.pagination.total = res.data.total || 0
          } else {
            // 兜底处理
            this.tableData = []
            this.pagination.total = 0
          }
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取策略记录失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.name) params.name = this.queryInput.name
      if (this.queryInput.operateType !== '') params.operateType = this.queryInput.operateType
      if (this.queryInput.status !== '') params.status = this.queryInput.status
      if (this.queryInput.recordTime && this.queryInput.recordTime.length > 0) {
        params.beginDate = this.queryInput.recordTime[0] + ' 00:00:00'
        params.endDate = this.queryInput.recordTime[1] + ' 23:59:59'
      }
      return params
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getStrategyRecordList()
    },
    handleReset() {
      this.queryInput = {
        name: '',
        recordTime: null,
        operateType: '',
        status: ''
      }
      this.handleQuery()
    },
    handleDelete(record) {
      this.$confirm('确定要删除选中协议记录吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const res = await deleteStrategyRecord({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getStrategyRecordList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm(`确定要删除选中策略记录吗?删除后不可恢复`, '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const recordIds = this.selectedRows.map(row => row.id)
            const res = await deleteStrategyRecord({ ids: recordIds.join(',') })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getStrategyRecordList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getStrategyRecordList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getStrategyRecordList()
    },
    formatTime(time) {
      if (time === '-' || !time) {
        return time
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    getStatusText(record) {
      if (record.status == "0" && record.operateType == "0") {
        return "下发失败"
      } else if (record.status == "1" && record.operateType == "0") {
        return "下发成功"
      } else if (record.status == "0" && record.operateType == "1") {
        return "同步失败"
      } else if (record.status == "1" && record.operateType == "1") {
        return "同步成功"
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
