import request from '@/util/request'

/**
 * 策略管理-查询接口
 * @param params
 * @returns {Promise}
 */
export function getStrategyList(params) {
  return request({
    url: '/dev/tactics/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 策略管理-新增接口
 * @param data
 * @returns {Promise}
 */
export function addStrategy(data) {
  return request({
    url: '/dev/tactics/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-编辑接口
 * @param data
 * @returns {Promise}
 */
export function updateStrategy(data) {
  return request({
    url: '/dev/tactics/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-查看
 * @param params
 * @returns {Promise}
 */
export function getStrategyInfo(params) {
  return request({
    url: '/dev/tactics/infor',
    method: 'post',
    data: params || {},
  })
}

/**
 * 策略管理-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteStrategy(data) {
  return request({
    url: '/dev/tactics/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-批量下发
 * @param data
 * @returns {Promise}
 */
export function issueStrategy(data) {
  return request({
    url: '/dev/tactics/protocolIssued',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-同步设备协议
 * @param data
 * @returns {Promise}
 */
export function syncStrategyFromDevice(data) {
  return request({
    url: '/dev/tactics/syncFromDevice',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-启停
 * @param data
 * @returns {Promise}
 */
export function toggleStrategy(data) {
  return request({
    url: '/dev/tactics/outset',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-应用范围
 * @param data
 * @returns {Promise}
 */
export function setStrategyScope(data) {
  return request({
    url: '/dev/tactics/applyConfine',
    method: 'post',
    data: data || {},
  })
}

/**
 * 策略管理-获取设备集合
 * @param params
 * @returns {Promise}
 */
export function getDeviceList(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}
