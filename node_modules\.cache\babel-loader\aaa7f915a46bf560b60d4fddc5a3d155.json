{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\component\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\component\\DeviceComponent.vue", "mtime": 1749194129327}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}