{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\component\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\component\\DeviceComponent.vue", "mtime": 1750058891542}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}